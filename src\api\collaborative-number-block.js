import { post } from '@/http/request'

// 协同号码拦截配置API

/**
 * 分页查询协同号码拦截配置
 * @param {Object} data - 查询参数
 * @param {number} data.pageType - 分页类型
 * @param {number} data.pageIndex - 页码
 * @param {number} data.pageSize - 每页数量
 * @param {string} data.phoneNumber - 号码模糊查询字段
 * @returns {Promise} 返回分页查询协同号码拦截配置请求的Promise对象
 */
export function getCollaborativeNumberBlockPage(data) {
  return post('/yc-govphonemgmt/webcall?action=collaborativeNumberBlock.page', { data })
}

/**
 * 新增协同号码拦截配置
 * @param {Object} data - 新增参数
 * @param {string} data.phoneNum - 号码
 * @param {string} data.remark - 备注
 * @returns {Promise} 返回新增协同号码拦截配置请求的Promise对象
 */
export function addCollaborativeNumberBlock(data) {
  return post('/yc-govphonemgmt/servlet/collaborativeNumberBlock?action=add', { data })
}

/**
 * 删除协同号码拦截配置
 * @param {Object} data - 删除参数
 * @param {string} data.numberId - 协同号码ID
 * @returns {Promise} 返回删除协同号码拦截配置请求的Promise对象
 */
export function deleteCollaborativeNumberBlock(data) {
  return post('/yc-govphonemgmt/servlet/collaborativeNumberBlock?action=delete', { data })
}
