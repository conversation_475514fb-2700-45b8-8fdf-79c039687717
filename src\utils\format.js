/**
 * 格式化工作时间段
 * @param {Object} data 包含工作时间信息的数据对象
 * @returns {String} 格式化后的工作时间字符串
 */
export function formatWorkTimeSlots(data) {
  if (!data) return "未设置";

  // 构建时间段数组
  const timeSlots = [];

  // 处理三个时间段
  if (data.workTime1Start && data.workTime1End) {
    timeSlots.push(`${data.workTime1Start}-${data.workTime1End}`);
  }
  if (data.workTime2Start && data.workTime2End) {
    timeSlots.push(`${data.workTime2Start}-${data.workTime2End}`);
  }
  if (data.workTime3Start && data.workTime3End) {
    timeSlots.push(`${data.workTime3Start}-${data.workTime3End}`);
  }

  return timeSlots.length > 0 ? timeSlots.join("、") : "未设置";
}

/**
 * 格式化工作日
 * @param {String} workDays 工作日字符串，如"1,2,3,4,5"
 * @returns {String} 格式化后的工作日字符串
 */
export function formatWorkDays(workDays) {
  if (!workDays) return "";

  const workDaysMap = {
    1: "周一",
    2: "周二",
    3: "周三",
    4: "周四",
    5: "周五",
    6: "周六",
    7: "周日",
  };

  const days = workDays.split(",").filter((day) => day.trim());
  if (days.length === 0) return "";

  if (
    days.length === 5 &&
    days.includes("1") &&
    days.includes("2") &&
    days.includes("3") &&
    days.includes("4") &&
    days.includes("5")
  ) {
    return "周一至周五";
  } else if (days.length === 7) {
    return "周一至周日";
  } else {
    return days
      .map((day) => workDaysMap[day])
      .filter(Boolean)
      .join("、");
  }
}

/**
 * 完整格式化工作时间，包括时间段、工作日和节假日设置
 * @param {Object} data 包含工作时间信息的数据对象
 * @returns {String} 格式化后的完整工作时间字符串
 */
export function formatWorkTime(data) {
  if (!data) return "未设置";

  // 构建完整的工作时间描述
  const parts = [];

  // 添加时间段
  const timeSlots = formatWorkTimeSlots(data);
  if (timeSlots !== "未设置") {
    parts.push(timeSlots);
  }

  // 添加工作日
  const workDaysText = formatWorkDays(data.workDays);
  if (workDaysText) {
    parts.push(workDaysText);
  }

  // 添加节假日设置
  if (data.skipHoliday == 1) {
    parts.push("跳过节假日");
  }

  return parts.join("、") || "未设置";
}

/**
 * 格式化协同号码
 * @param {Object} data 包含协同号码信息的数据对象
 * @returns {String} 格式化后的协同号码字符串
 */
export function formatCollaborativeNumbers(data) {
  if (!data) return "暂无数据";

  const numbers = [];

  // 添加协同号码1
  if (data.collaborativeNumber1 && data.collaborativeNumber1.trim()) {
    numbers.push(data.collaborativeNumber1.trim());
  }

  // 添加协同号码2
  if (data.collaborativeNumber2 && data.collaborativeNumber2.trim()) {
    numbers.push(data.collaborativeNumber2.trim());
  }

  return numbers.length > 0 ? numbers.join("、") : "暂无数据";
}
