import { post } from "@/http/request";

// 审核管理API

/**
 * 审核管理列表
 * @param {string} action - 接口action
 * @param {Object} params - 查询参数
 * @param {string} params.date - 申请时间
 * @param {string} params.businessType - 业务类型
 * @returns {Promise} 返回审核管理列表的Promise对象
 */
export function getAuditList(action, data) {
  return post(
    "/yc-govphonemgmt/webcall",
    { data },
    {
      action: `auditManagementDao.${action}`,
    }
  );
}

/**
 * 审核操作
 * @param {string} action - 接口action
 * @param {Object} data - 参数
 * @param {string} data.id - 申请ID
 * @param {string} data.applicationNo - 申请编码
 * @param {string} data.instanceId - 当前流程实例ID
 * @param {string} data.approvalDesc - 处理描述
 * @param {string} data.auditStatus - 审核状态（2=通过，3=不通过）
 * @param {string} data.currentNodeName - 当前节点名称
 * @param {integer} data.currentStep - 当前步骤，从0开始
 * @param {integer} data.totalSteps - 总步骤
 * @param {string} data.busiType - 业务类型
 * @returns {Promise} 返回审核管理列表的Promise对象
 */
export function auditApply(data) {
  return post(
    "/yc-govphonemgmt/servlet/auditManagement",
    { data },
    {
      action: "audit",
    }
  );
}
