<template>
  <el-dialog
    title="协同号码配置申请"
    width="700px"
    :visible.sync="visible"
    @close="handleClose">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="100px">
        <!-- 协同号码1 -->
        <el-form-item
          label="协同号码1"
          prop="collaborativeNumber1">
          <el-input
            v-model="form.collaborativeNumber1"
            placeholder="请输入"
            size="medium"
            clearable>
          </el-input>
        </el-form-item>

        <!-- 协同号码2 -->
        <el-form-item
          label="协同号码2"
          prop="collaborativeNumber2">
          <el-input
            v-model="form.collaborativeNumber2"
            placeholder="请输入"
            size="medium"
            clearable>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button
        type="primary"
        size="small"
        plain
        @click="handleClose"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        @click="handleSubmit">
        提交申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { getCollaborativeNumberByPhoneId, updateCollaborativeNumber } from '@/api/number-manage'

export default {
  name: 'CooperativeNumberDialog',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => null,
    },
    selection: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      form: {
        collaborativeNumber1: '',
        collaborativeNumber2: '',
      },
      rules: {
        collaborativeNumber1: [{ required: true, message: '请输入协同号码1', trigger: 'blur' }],
        collaborativeNumber2: [{ required: true, message: '请输入协同号码2', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler(data) {
        if (data) {
          this.form = {
            collaborativeNumber1: data.collaborativeNumber1,
            collaborativeNumber2: data.collaborativeNumber2,
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    reset() {
      this.form = {
        collaborativeNumber1: '',
        collaborativeNumber2: '',
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true

      // 调用修改协同号码接口
      const [err, res] = await updateCollaborativeNumber({
        ...this.form,
        phoneId: this.selection.join(),
        collaborativeNumId: this.data?.id,
      })

      if (res) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }
      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
