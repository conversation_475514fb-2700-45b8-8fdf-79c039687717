import { get, post } from '@/http/request'

// 号码管理API

/**
 * 号码列表查询功接口
 * @param {Object} data - 查询参数
 * @param {string} data.groupId - 单位ID
 * @param {number} data.numberSource - 号码来源：1-AS平台，2-第三方呼叫中心
 * @param {number} data.numberType - 号码类型：1-统签，2-散号
 * @param {string} data.number - 号码
 * @param {boolean} data.hasWorkTime - 是否设置工作时间（全部就不传或者null）
 * @param {boolean} data.hasWelcomeWord - 是否设置欢迎语
 * @param {boolean} data.hasBusyTip - 是否设置遇忙提示
 * @param {boolean} data.hasNoWorkTimeTip - 是否设置非工作时间提示语
 * @param {boolean} data.hasSatisfactionEvaluation - 是否开启满意度评价开关
 * @param {boolean} data.hasCollaboratePhone - 是否配置协同号码
 * @param {number} data.concurrentCallLimit - 并发数（没有就不传或者null）
 * @returns {Promise} 返回号码列表查询请求的Promise对象
 */
export function getPhoneNumberList(data) {
  return post('/yc-govphonemgmt/webcall?action=phoneNumber.list', { data })
}

/**
 * 新增号码
 * @param {Object} data - 新增号码参数
 * @param {string} data.phoneNumbers - 号码，多个用逗号间隔
 * @param {string} data.groupId - 单位ID
 * @param {string} data.groupName - 单位名
 * @param {number} data.numberSource - 号码来源：1-AS平台，2-第三方呼叫中心
 * @param {number} data.numberType - 号码类型：1-统签，2-散号
 * @returns {Promise} 返回新增号码请求的Promise对象
 */
export function addPhoneNumber(data) {
  return post('/yc-govphonemgmt/servlet/phoneNumber?action=phoneNumberAdd', { data })
}

/**
 * 删除号码
 * @param {Object} data - 删除号码参数
 * @param {string} data.phoneNumberIds - 号码id 多个用逗号拼接
 * @returns {Promise} 返回删除号码请求的Promise对象
 */
export function deletePhoneNumber(data) {
  return post('/yc-govphonemgmt/servlet/phoneNumber?action=phoneNumberDelete', { data })
}

/**
 * 号码导入
 * @param {File} file - excel文件
 * @returns {Promise} 返回号码导入请求的Promise对象
 */
export function importPhoneNumber(file) {
  const formData = new FormData()
  formData.append('file', file)

  return post('/yc-govphonemgmt/servlet/phoneNumber?action=import', formData, {}, {
    'Content-Type': 'multipart/form-data'
  })
}

/**
 * 号码导出
 * @param {Object} data - 导出参数
 * @param {string} data.groupId - 单位ID
 * @param {number} data.numberSource - 号码来源：1-AS平台，2-第三方呼叫中心
 * @param {number} data.numberType - 号码类型：1-统签，2-散号
 * @param {string} data.number - 号码
 * @returns {Promise} 返回号码导出请求的Promise对象
 */
export function exportPhoneNumber(data) {
  return post('/yc-govphonemgmt/servlet/phoneNumber?action=export', { data })
}

/**
 * 工作时间列表查询
 * @param {Object} data - 查询参数
 * @param {string} data.phoneNumberId - 号码ID
 * @returns {Promise} 返回工作时间列表查询请求的Promise对象
 */
export function getWorkTimeList(data) {
  return post('/yc-govphonemgmt/webcall?action=workTime.list', { data })
}

/**
 * 新增工作时间
 * @param {Object} data - 新增工作时间参数
 * @param {string} data.phoneNumberIds - 号码ID,多个用,间隔
 * @param {string} data.workTime1Start - 工作时间1开始时间，格式HH:mm:ss
 * @param {string} data.workTime1End - 工作时间1结束时间，格式HH:mm:ss
 * @param {string} data.workTime2Start - 工作时间2开始时间，格式HH:mm:ss
 * @param {string} data.workTime2End - 工作时间2结束时间，格式HH:mm:ss
 * @param {string} data.workTime3Start - 工作时间3开始时间，格式HH:mm:ss
 * @param {string} data.workTime3End - 工作时间3结束时间，格式HH:mm:ss
 * @param {string} data.workDays - 工作日，例如：1,2,3,4,5,6,7 表示周一到周日
 * @param {number} data.skipHoliday - 是否跳过节假日：0-否，1-是
 * @param {string} data.isEnabled - 是否启用  0:不启用 1:启用
 * @returns {Promise} 返回新增工作时间请求的Promise对象
 */
export function addWorkTime(data) {
  return post('/yc-govphonemgmt/servlet/workTime?action=add', { data })
}

/**
 * 删除工作时间
 * @param {Object} data - 删除工作时间参数
 * @param {string} data.workTimeId - 工作时长ID
 * @returns {Promise} 返回删除工作时间请求的Promise对象
 */
export function deleteWorkTime(data) {
  return post('/yc-govphonemgmt/servlet/workTime?action=delete', { data })
}

/**
 * 更新工作时间
 * @param {Object} data - 更新工作时间参数
 * @param {string} data.workTimeId - 工作时长ID
 * @param {string} data.workTime1Start - 工作时间1开始时间，格式HH:mm:ss
 * @param {string} data.workTime1End - 工作时间1结束时间，格式HH:mm:ss
 * @param {string} data.workTime2Start - 工作时间2开始时间，格式HH:mm:ss
 * @param {string} data.workTime2End - 工作时间2结束时间，格式HH:mm:ss
 * @param {string} data.workTime3Start - 工作时间3开始时间，格式HH:mm:ss
 * @param {string} data.workTime3End - 工作时间3结束时间，格式HH:mm:ss
 * @param {string} data.workDays - 工作日，例如：1,2,3,4,5,6,7 表示周一到周日
 * @param {number} data.skipHoliday - 是否跳过节假日：0-否，1-是
 * @param {string} data.isEnabled - 是否启用  0:不启用 1:启用
 * @returns {Promise} 返回更新工作时间请求的Promise对象
 */
export function updateWorkTime(data) {
  return post('/yc-govphonemgmt/servlet/workTime?action=update', { data })
}

/**
 * 临时延长工作时间列表查询
 * @param {Object} data - 查询参数
 * @param {string} data.phoneNumberId - 号码ID
 * @returns {Promise} 返回临时延长工作时间列表查询请求的Promise对象
 */
export function getTempExtendWorkTimeList(data) {
  return post('/yc-govphonemgmt/webcall?action=tempExtendWorkTime.list', { data })
}

/**
 * 新增临时延长工作时间
 * @param {Object} data - 新增临时延长工作时间参数
 * @param {string} data.phoneNumIds - 号码ID，多个用,间隔
 * @param {number} data.extendMinutes - 延长时长（分钟）
 * @returns {Promise} 返回新增临时延长工作时间请求的Promise对象
 */
export function addTempExtendWorkTime(data) {
  return post('/yc-govphonemgmt/servlet/tempExtendWorkTime?action=add', { data })
}

/**
 * 通过ID获取号码信息
 * @param {Object} data - 查询参数
 * @param {string} data.phoneId - 号码ID
 * @returns {Promise} 返回号码详细信息请求的Promise对象
 */
export function getPhoneNumberById(data) {
  return post('/yc-govphonemgmt/webcall?action=phoneNumber.getById', { data })
}

/**
 * 修改满意度开关
 * @param {Object} data - 修改满意度开关参数
 * @param {string} data.id - id
 * @param {number} data.status - 状态 开关状态：0-关闭，1-开启
 * @returns {Promise} 返回修改满意度开关请求的Promise对象
 */
export function updateSatisfaction(data) {
  return post('/yc-govphonemgmt/servlet/Satisfaction?action=update', { data })
}

/**
 * 录音文件列表查询
 * @param {Object} data - 查询参数
 * @param {string} data.phoneId - 号码ID
 * @returns {Promise} 返回录音文件列表查询请求的Promise对象
 */
export function getAudioFileList(data) {
  return post('/yc-govphonemgmt/webcall?action=audioFile.list', { data })
}

/**
 * 获取录音文件路径
 * @param {string} id - 录音文件ID
 * @returns {string} 返回录音文件路径
 */
export function getAudioFilePath(id) {
  // return '/test.mp3'
  return '/yc-govphonemgmt/servlet/audioFile?action=getAudioFile&audioId=' + id
}

/**
 * 新增音频文件
 * @param {File} file - 音频文件
 * @param {string} phoneIds - 号码ID，多个用逗号间隔
 * @param {string} audioType - 音频类型：1-欢迎语，2-遇忙提示，3-非工作时间提示
 * @returns {Promise} 返回新增音频文件请求的Promise对象
 */
export function addAudioFile(formData) {
  return post('/yc-govphonemgmt/servlet/audioFile?action=add', formData, {}, {
    'Content-Type': 'multipart/form-data'
  })
}

/**
 * 修改音频文件
 * @param {string} audioId - 音频ID
 * @param {File} file - 音频文件
 * @returns {Promise} 返回修改音频文件请求的Promise对象
 */
export function updateAudioFile(formData) {
  return post('/yc-govphonemgmt/servlet/audioFile?action=update', formData, {}, {
    'Content-Type': 'multipart/form-data'
  })
}

/**
 * 删除音频文件
 * @param {Object} data - 删除音频文件参数
 * @param {string} data.audioId - 音频ID
 * @returns {Promise} 返回删除音频文件请求的Promise对象
 */
export function deleteAudioFile(data) {
  return post('/yc-govphonemgmt/servlet/audioFile?action=delete', { data })
}

/**
 * 白名单列表分页查询
 * @param {Object} data - 查询参数对象
 * @param {string} [data.phoneId] - 主号码ID（可选）
 * @param {string} [data.phoneNum] - 号码模糊查询字段（可选）
 * @param {number} [data.pageType] - 分页类型（可选）
 * @param {number} [data.pageIndex] - 页码（可选）
 * @param {number} [data.pageSize] - 每页数量（可选）
 * @returns {Promise<Object>} 返回白名单分页数据的Promise对象，包含data数组和totalRow总数
 */
export function getWhitelistPage(data) {
  return post('/yc-govphonemgmt/webcall?action=recordingWhitelist.page', { data })
}

/**
 * 新增/批量新增白名单
 * @param {Object} data - 参数对象
 * @param {string} data.whitePhoneNums - 白名单号码列表，多个用逗号间隔
 * @param {string} data.phoneId - 主号码ID
 * @param {string} data.remark - 备注
 * @param {string} data.effectiveTime - 生效时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {string} data.expiryTime - 失效时间，格式：yyyy-MM-dd HH:mm:ss
 * @returns {Promise<Object>} 返回新增白名单操作结果的Promise对象
 */
export function addWhitelist(data) {
  return post('/yc-govphonemgmt/servlet/recordingWhitelist?action=batchAdd', { data })
}

/**
 * 白名单号码导入
 * @param {File} file - excel文件
 * @returns {Promise<Object>} 返回导入操作结果的Promise对象
 */
export function importWhitelist(file) {
  const formData = new FormData()
  formData.append('file', file)
  return post('/yc-govphonemgmt/servlet/recordingWhitelist?action=import', formData, {}, {
    'Content-Type': 'multipart/form-data'
  })
}

/**
 * 删除/批量删除白名单
 * @param {Object} data - 参数对象
 * @param {string} data.phoneId - 主号码ID
 * @param {string} data.whitelistPhoneIds - 白名单ID，多个用逗号间隔
 * @returns {Promise} 返回Promise对象
 */
export function deleteWhitelist(data) {
  return post('/yc-govphonemgmt/servlet/recordingWhitelist?action=batchDelete', { data })
}

/**
 * 录音黑名单分页查询
 * @param {Object} data - 查询参数对象
 * @param {string} data.phoneId - 主号码ID
 * @param {string} [data.phoneNum] - 号码模糊查询字段（可选）
 * @param {number} data.pageType - 分页类型
 * @param {number} data.pageIndex - 页码
 * @param {number} data.pageSize - 每页数量
 * @returns {Promise} 返回Promise对象
 */
export function getBlacklistPage(data) {
  return post('/yc-govphonemgmt/webcall?action=incomingBlacklist.page', { data })
}

/**
 * 新增/批量新增黑名单
 * @param {Object} data - 参数对象
 * @param {string} data.blackPhoneNums - 黑名单号码列表，多个用逗号间隔
 * @param {string} data.phoneId - 主号码ID
 * @param {string} data.remark - 备注
 * @param {string} data.effectiveTime - 生效时间，格式：yyyy-MM-dd HH:mm:ss
 * @param {string} data.expiryTime - 失效时间，格式：yyyy-MM-dd HH:mm:ss
 * @returns {Promise} 返回Promise对象
 */
export function addBlacklist(data) {
  return post('/yc-govphonemgmt/servlet/incomingBlacklist?action=batchAdd', { data })
}

/**
 * 黑名单导入
 * @param {File} file - excel文件
 * @returns {Promise} 返回Promise对象
 */
export function importBlacklist(file) {
  const formData = new FormData()
  formData.append('file', file)
  return post('/yc-govphonemgmt/servlet/incomingBlacklist?action=import', formData, {}, {
    'Content-Type': 'multipart/form-data'
  })
}

/**
 * 删除/批量删除录音黑名单
 * @param {Object} data - 参数对象
 * @param {string} data.phoneId - 主号码ID
 * @param {string} data.blacklistPhoneIds - 黑名单ID，多个用逗号间隔
 * @returns {Promise} 返回Promise对象
 */
export function deleteBlacklist(data) {
  return post('/yc-govphonemgmt/servlet/incomingBlacklist?action=batchDelete', { data })
}

/**
 * 修改/批量修改黑名单列表
 * @param {Object} data - 参数对象
 * @param {string} data.phoneId - 主号码ID
 * @param {string} data.blacklistPhoneIds - 黑名单ID，多个用逗号间隔
 * @param {string} [data.remark] - 备注（可选）
 * @param {string} [data.effectiveTime] - 生效时间（可选）
 * @param {string} [data.expiryTime] - 失效时间（可选）
 * @returns {Promise} 返回Promise对象
 */
export function updateBlacklist(data) {
  return post('/yc-govphonemgmt/servlet/incomingBlacklist?action=batchUpdate', { data })
}

/**
 * 通过手机号获取协同号码信息
 * @param {Object} data - 参数对象
 * @param {string} data.phoneId - 主号码ID
 * @returns {Promise} 返回Promise对象
 */
export function getCollaborativeNumberByPhoneId(data) {
  return post('/yc-govphonemgmt/webcall?action=collaborativeNumber.getByPhoneId', { data })
}

/**
 * 修改协同号码
 * @param {Object} data - 参数对象
 * @param {string} data.phoneId - 主号码ID
 * @param {string} data.collaborativeNumId - 协同号码ID
 * @param {string} data.collaborativeNumber1 - 协同号码1
 * @param {string} data.collaborativeNumber2 - 协同号码2
 * @returns {Promise} 返回Promise对象
 */
export function updateCollaborativeNumber(data) {
  return post('/yc-govphonemgmt/servlet/collaborativeNumber?action=update', { data })
}

/**
 * 清空协同号码
 * @param {Object} data - 参数对象
 * @param {string} data.phoneId - 主号码ID
 * @param {string} data.collaborativeNumId - 协同号码ID
 * @returns {Promise} 返回Promise对象
 */
export function clearCollaborativeNumber(data) {
  return post('/yc-govphonemgmt/servlet/collaborativeNumber?action=clear', { data })
}

/**
 * 修改通话并发数
 * @param {Object} data - 参数对象
 * @param {string} data.phoneId - 手机号ID
 * @param {number} data.concurrentNum - 并发数
 * @returns {Promise} 返回Promise对象
 */
export function updateConcurrentCall(data) {
  return post('/yc-govphonemgmt/servlet/concurrentCall?action=update', { data })
}