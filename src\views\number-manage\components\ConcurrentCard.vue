<template>
  <base-card class="concurrent-card card-container y-container">
    <div class="info">
      <span class="text-2xl font-bold">{{ data.concurrentCallLimit || 1 }}</span>
      <span class="label">通话并发数（默认最小为1）</span>
      <div class="y-bar no-padding mt-4">
        <el-button
          type="primary"
          plain
          size="small"
          @click="$emit('edit')"
          >修改</el-button
        >
      </div>
    </div>
  </base-card>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/modules/info-card.scss';

.card-container {
  padding: 16px 24px;
  background: rgba(5, 85, 206, 0.05) url('@/assets/images/number/concurrent-bg.svg') no-repeat center center / cover;

  .label {
    color: $txtColor-light;
  }
}
</style>
