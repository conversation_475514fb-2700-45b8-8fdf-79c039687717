<template>
  <el-dialog
    title="通话并发数配置申请"
    width="420px"
    :visible.sync="visible"
    @close="handleClose">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="120px">
        <!-- 通话并发数 -->
        <el-form-item
          label="通话并发数"
          prop="concurrentNum">
          <el-input-number
            v-model="form.concurrentNum"
            :min="1"
            :max="1000"
            :step="1"
            size="medium"
            controls-position="right">
          </el-input-number>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button
        type="primary"
        size="small"
        plain
        @click="handleClose"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        @click="handleSubmit">
        提交申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { updateConcurrentCall } from '@/api/number-manage'

export default {
  name: 'ConcurrentCallsDialog',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => null,
    },
    selection: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      form: {
        concurrentNum: 1,
      },
      rules: {
        concurrentNum: [{ required: true, message: '请输入通话并发数', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler(data) {
        if (data) {
          this.form = {
            concurrentNum: data.concurrentCallLimit,
          }
        } else {
          this.reset()
        }
      },
      immediate: true,
    },
  },
  methods: {
    reset() {
      this.form = {
        concurrentNum: 1,
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true

      // 调用修改通话并发数接口
      const [err, res] = await updateConcurrentCall({
        ...this.form,
        phoneId: this.selection.join(),
      })

      if (res) {
        this.$message({
          message: '操作',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }
      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
