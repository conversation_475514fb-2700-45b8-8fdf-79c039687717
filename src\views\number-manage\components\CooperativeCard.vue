<template>
  <base-card
    class="concurrent-card card-container y-container"
    border>
    <div class="info">
      <h4 class="y-title">
        <el-image
          :src="require('@/assets/images/number/cooperative-icon.svg')"
          style="width: 40px; height: 40px; vertical-align: middle" />
        <span class="ml-2 text-2xl align-middle">
          {{ formatCollaborativeNumbers(data) }}
        </span>
      </h4>
      <div class="body">
        <span class="label">1.必须是联通号码</span>
        <span class="label">2.必须不属于平台号码</span>
        <span class="label">3.必须不属于协同号码拦截库号码</span>
      </div>
    </div>
    <div class="btn-set">
      <el-button
        type="danger"
        plain
        size="small"
        @click="$emit('clear')" 
        >清空</el-button
      >
      <el-button
        type="primary"
        plain
        size="small"
        @click="$emit('edit')"
        >修改</el-button
      >
    </div>
  </base-card>
</template>

<script>
import {formatCollaborativeNumbers} from '@/utils/format'

export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  methods: {
    formatCollaborativeNumbers
  },
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/modules/info-card.scss';

.card-container {
  padding: 16px 24px;
  background: $bgColor;

  .info {
    .body {
      @include flex-col(flex-start, flex-start);
      padding: 16px;
      border-radius: 4px;
      background: linear-gradient(270deg, rgba(5, 85, 206, 0) 0%, rgba(5, 85, 206, 0.1) 100%);
    }
  }

  .btn-set {
    align-items: flex-start;
  }

  .label {
    color: $txtColor-light;
  }
}
</style>
