<template>
  <div class="h-full flex flex-col">
    <div class="bg-white rounded mb-4 relative">
      <div class="tab-box">
        <el-tabs class="px-6" v-model="tabActive">
          <el-tab-pane
            v-for="item in tabList"
            :key="item.name"
            :label="item.label"
            :name="item.name"
          />
        </el-tabs>
      </div>

      <el-form
        class="px-6"
        ref="searchForm"
        :inline="true"
        :model="searchForm"
        size="small"
        label-width="auto"
      >
        <el-form-item label="申请时间" prop="date">
          <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            valueFormat="yyyy-MM-dd"
            unlink-panels
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="业务类型" prop="business_type">
          <el-select
            v-model="searchForm.business_type"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in businessTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 审核列表不显示 -->
        <el-form-item
          v-if="fromWhere !== 'auditList'"
          label="关闭状态"
          prop="closeStatus"
        >
          <el-select
            v-model="searchForm.closeStatus"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in closeStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="resetSearch()">
            <svg-icon icon="reset"></svg-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleSearch()">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <empty-wrapper
      class="flex-1 overflow-hidden rounded relative"
      :toggle="!applyListData?.length"
      v-loading="loading"
      element-loading-text="加载中..."
    >
      <div class="overflow-auto grid grid-cols-4 gap-4 box-list">
        <div v-for="item in applyListData" :key="item.ID" class="box">
          <div
            class="state"
            :style="handleTabActiveContent('style', tabActive, 'name')"
          >
            {{ handleTabActiveContent("label", tabActive, "name") }}
          </div>

          <div class="flex items-center">
            <img
              class="avatar"
              src="@/assets/images/my-apply/apply-avatar.png"
            />

            <div class="phone">
              <div class="text-xl font-bold truncate mb-1">
                {{
                  item.info?.phoneNumber ||
                  item.phoneList?.map((item) => item.phoneNumber).join("、")
                }}
              </div>

              <div class="businessType">
                <div class="label">业务类型：</div>
                <div
                  class="businessType-value"
                  :style="handleBusinessTypeContent('style', item.businessType)"
                >
                  {{ handleBusinessTypeContent("label", item.businessType) }}
                </div>
              </div>
            </div>
          </div>

          <div class="box_middle mt-4 mb-2">
            <div>
              <div class="label">申请编号：</div>
              <div class="truncate">
                {{ item.applicationNo }}
              </div>
            </div>
            <div>
              <div class="label">申请人：</div>
              <div class="truncate">
                {{ item.applicantName }}
              </div>
            </div>
            <div>
              <div class="label">申请时间：</div>
              <div class="truncate">
                {{ item.applicationTime }}
              </div>
            </div>
            <!-- 审核列表不显示 -->
            <div v-if="fromWhere !== 'auditList'">
              <div class="label">关闭状态：</div>
              <div class="blue truncate">
                {{ handleCloseStatusContent(item.closeStatus) }}
              </div>
            </div>
          </div>

          <div class="box_description mb-4">
            <div>
              <div class="label">申请说明：</div>
              <div class="truncate">
                {{ item.applicationDesc }}
              </div>
            </div>
          </div>

          <div class="text-right">
            <el-button type="primary" size="small" @click="handleDetail(item)">
              {{ tabActive === "auditWaitList" ? "审核" : "查看详情" }}
            </el-button>
          </div>
        </div>
      </div>

      <div class="pagination">
        <pagination
          :current-page.sync="pageData.pageNumber"
          :page-size.sync="pageData.pageSize"
          :total="pageData.total"
          @page="handleSearch()"
        >
        </pagination>
      </div>
    </empty-wrapper>
  </div>
</template>

<script>
import { getAuditList } from "@/api/audit-manage.js";
import { getMyApplyList } from "@/api/my-apply.js";
import applyMixins from "../mixins/index.js";

export default {
  name: "ApplyList",
  mixins: [applyMixins],
  props: {
    fromWhere: {
      type: String,
      default: "applyList",
    },
  },
  data() {
    return {
      loading: false,
      tabActive: "",
      date: [],
      searchForm: {
        begin_date: "",
        end_date: "",
        business_type: "",
        closeStatus: "",
      },
      applyListData: [],
      pageData: {
        pageNumber: 1,
        pageSize: 15,
        total: 0,
      },
    };
  },
  watch: {
    tabActive(val) {
      this.resetSearch();
      this.handleSearch(true);
    },

    date(val) {
      this.searchForm.begin_date = val?.[0] || "";
      this.searchForm.end_date = val?.[1] || "";
    },
  },
  mounted() {
    this.tabActive =
      this.fromWhere === "auditList" ? "auditWaitList" : "queryApplyIngList";
  },
  methods: {
    // 搜索
    async handleSearch(isRestPage = false) {
      try {
        this.loading = true;
        if (isRestPage) {
          this.pageData = this.$options.data().pageData;
        }

        const fn =
          this.fromWhere === "auditList" ? getAuditList : getMyApplyList;

        let params = this.searchForm;
        if (this.fromWhere === "auditList") {
          const {
            business_type: businessType,
            begin_date: startTime,
            end_date: endTime,
          } = this.searchForm;
          params = { businessType, startTime, endTime };
        }

        const [err, res] = await fn(this.tabActive, {
          ...params,
          ...this.pageData,
          pageType: "3",
          pageNum: this.pageData.pageNumber,
        });

        this.applyListData = res?.data || [];
        this.pageData.total = res?.totalRow || 0;
      } finally {
        this.loading = false;
      }
    },

    // 重置表单
    resetSearch() {
      this.$refs.searchForm.resetFields();
      this.date = [];
    },

    // 查看详情
    handleDetail(item) {
      const {
        businessType,
        phoneNumber,
        id,
        applicationNo,
        instanceId,
        currentNodeName,
        currentStep,
        totalSteps,
      } = item;

      let action = this.handleBusinessTypeContent("action", businessType);

      // 号码操作需要判断单个号码或多个号码
      if (businessType == "1") {
        const phoneLen = phoneNumber?.split(",")?.length || 0;
        action += phoneLen > 1 ? "2" : "1";
      }

      let auditParams = {};
      if (this.fromWhere === "auditList") {
        auditParams = {
          businessType,
          id,
          applicationNo,
          instanceId,
          currentNodeName,
          currentStep,
          totalSteps,
        };
      }

      this.$router.push({
        path: "/my-apply/apply-detail",
        query: {
          id,
          action,
          fromWhere: this.fromWhere,
          auditParams: JSON.stringify(auditParams),
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.tab-box) {
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;

  .el-tabs__header {
    margin-bottom: 0px;

    .el-tabs__nav-wrap::after {
      display: none;
    }
  }
}

:deep(.el-date-editor) {
  .el-input__icon {
    line-height: 32px;
  }

  .el-range-separator {
    line-height: 32px;
  }
}

.box-list {
  height: calc(100% - 57px);
}

.box {
  position: relative;
  overflow: hidden;
  padding: 24px;
  height: min-content;
  border-radius: 4px;
  background: #ffffff;

  .state {
    position: absolute;
    top: 15px;
    right: -45px;
    padding: 5px 0;
    width: 150px;
    text-align: center;
    transform: rotate(45deg);
  }

  .avatar {
    margin-right: 24px;
    width: 72px;
    height: 72px;
    border-radius: 50%;
  }

  .phone {
    width: calc(100% - 105px);

    .businessType {
      @include flex(flex-start);

      &-value {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 4px;
      }
    }
  }
}

.box_middle,
.box_description {
  padding: 16px;
  background: rgba(5, 85, 206, 0.05);
  border-radius: 4px;

  > div {
    @include flex(flex-start);

    &:nth-child(n + 2) {
      margin-top: 8px;
    }

    > :first-child {
      flex-shrink: 0;
      width: 70px;
    }
  }
}

:deep(.pagination) {
  position: absolute;
  bottom: 0;
  padding: 12px 0;
  width: 100%;
  background: #ffffff;
  border-top: 1px solid #e8e8e8;

  .el-pagination {
    text-align: right;

    .el-pagination__sizes {
      margin-right: 0;
    }
  }
}

.label {
  color: $txtColor-light;
}

.blue {
  color: $themeColor;
}
</style>
