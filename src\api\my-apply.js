import { post } from "@/http/request";

// 我的申请API

/**
 * 我的申请列表
 * @param {string} action - 接口action
 * @param {Object} params - 查询参数
 * @param {string} params.date - 申请时间
 * @param {string} params.businessType - 业务类型
 * @param {string} params.state - 状态
 * @returns {Promise} 返回我的申请列表的Promise对象
 */
export function getMyApplyList(action, params) {
  return post(
    "/yc-govphonemgmt/webcall",
    {},
    { action: `ApplyDao.${action}`, ...params }
  );
}

/**
 * 申请详情
 * @param {string} action - 接口action
 * @param {Object} data - 查询参数
 * @param {string} data.applicationId - 申请id
 * @returns {Promise} 返回申请详情的Promise对象
 */
export function getApplyDetail(action, data) {
  return post("/yc-govphonemgmt/webcall", { data }, { action });
}

/**
 * 查询多个号码列表
 * @param {Object} data - 查询参数
 * @param {string} data.applicationId - 申请id
 * @param {string} data.pageIndex - 页数
 * @param {string} data.pageSize - 页码
 * @returns {Promise} 返回查询多个号码列表的Promise对象
 */
export function getMultipleNumbersList(data) {
  return post(
    "/yc-govphonemgmt/webcall",
    { data },
    { action: "ApplyDao.queryApplyPhoneDetail" }
  );
}

/**
 * 关闭申请
 * @param {Object} data - 请求参数
 * @param {string} data.applicationId - 申请id
 * @returns {Promise} 返回关闭申请的Promise对象
 */
export function closeApply(data) {
  return post(`/yc-govphonemgmt/servlet/apply?action=CloseApplication`, {
    data,
  });
}

/**
 * 修改工作时间
 * @param {Object} data -修改工作时间参数
 * @param {string} data.id - 申请id
 * @param {string} data.workTime1Start - 工作时间1开始时间，格式HH:mm:ss
 * @param {string} data.workTime1End - 工作时间1结束时间，格式HH:mm:ss
 * @param {string} data.workTime2Start - 工作时间2开始时间，格式HH:mm:ss
 * @param {string} data.workTime2End - 工作时间2结束时间，格式HH:mm:ss
 * @param {string} data.workTime3Start - 工作时间3开始时间，格式HH:mm:ss
 * @param {string} data.workTime3End - 工作时间3结束时间，格式HH:mm:ss
 * @param {string} data.workDays - 工作日，例如：1,2,3,4,5,6,7 表示周一到周日
 * @param {number} data.skipHoliday - 是否跳过节假日：0-否，1-是
 * @param {number} data.isEnabled - 是否启用  0:不启用 1:启用
 * @returns {Promise} 返回修改工作时间的Promise对象
 */
export function myApplyUpdateWorkTime(data) {
  return post("/yc-govphonemgmt/webcall/servlet", data, {
    action: "AgainApplyWork",
  });
}

/**
 * 修改满意度开关
 * @param {Object} data - 修改满意度开关参数
 * @param {string} data.applicationId - 申请id
 * @param {number} data.switchStatus - 状态 开关状态：0-关闭，1-开启
 * @returns {Promise} 返回修改满意度开关的Promise对象
 */
export function myApplyUpdateSatisfaction(data) {
  return post(
    "/yc-govphonemgmt/servlet/Satisfaction",
    { data },
    { action: "ResubmitSwitchApplication" }
  );
}

/**
 * 修改协同号码
 * @param {Object} data - 修改协同号码参数
 * @param {string} data.applicationId - 申请id
 * @param {string} data.phoneId - 号码id
 * @param {string} data.busiId - 协同号码表id
 * @param {string} data.applicationNo - 申请编号
 * @param {string} data.collaborativeNumber1 - 协同号码1
 * @param {string} data.collaborativeNumber2 - 协同号码2
 * @returns {Promise} 返回修改协同号码的Promise对象
 */
export function myApplyUpdateCollaborativeNumber(data) {
  return post(
    "/yc-govphonemgmt/servlet/synergyNumber",
    { data },
    { action: "update" }
  );
}

/**
 * 修改通话并发数
 * @param {Object} data - 修改通话并发数参数
 * @param {string} data.applicationId - 申请id
 * @param {string} data.phoneId - 号码id
 * @param {string} data.busiId - 通话并发数表id
 * @param {string} data.applicationNo - 申请编号
 * @param {string} data.concurrentCallLimit - 通话并发数
 * @returns {Promise} 返回修改通话并发数的Promise对象
 */
export function myApplyUpdateConcurrentCall(data) {
  return post(
    "/yc-govphonemgmt/servlet/callNumber",
    { data },
    { action: "update" }
  );
}
