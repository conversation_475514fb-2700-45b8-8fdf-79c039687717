<template>
  <div class="aside-bar">
    <div class="y-header h-[56px]">
      <h2 class="y-title">组织机构</h2>
    </div>
    <class-tree
      ref="tree"
      :tree="menuList"
      id-prop="deptId"
      :active-item="activeItem"
      highlight-current
      @set-current="setCurrent($event)">
      <template #default="{ node, data }">
        <svg-icon
          v-show="getNodeIcon(node, 'icon')"
          :class="['node-icon', getNodeIcon(node, 'icon') ? 'fill' : '']"
          :icon="getNodeIcon(node, 'icon')"></svg-icon>
        <span class="node-name">{{ data.deptName }}</span>
      </template>
    </class-tree>
  </div>
</template>

<script>
import { getDeptTree } from '@/api/org'
import ClassTree from '@/components/ClassTree'

export default {
  components: {
    ClassTree,
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      activeItem: null,
      keyword: '',
      menuList: [],
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      const [err, res] = await getDeptTree()
      if (res) {
        this.menuList = res.data || []
        if (this.menuList.length && this.menuList[0]?.children && this.menuList[0]?.children.length) {
          this.setCurrent(this.menuList[0])
        }
      }

      this.loading = false
    },
    setCurrent(item) {
      console.log(item)
      if (!item) return
      this.activeItem = item.deptId
      this.$emit('set-current', item)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.deptName.indexOf(value) !== -1
    },
    async submitForm() {
      if (!this.$refs.form) return

      this.$refs.form.validate(async (valid) => {
        if (!valid) return false

        const payload = { ...this.editForm }

        this.submitLoading = true
        try {
          // api ...
          this.$message({
            message: '操作成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.dialogShow = false
              this.$refs.form?.resetFields()
              this.fetchData()
            },
          })
        } catch (err) {
          console.error(err)
        } finally {
          this.submitLoading = false
        }
      })
    },
    getNodeIcon(node, type) {
      const map = {
        icon: ['minus-round', 'plus-round'],
      }
      if (node.childNodes.length > 0) {
        if (node.expanded) {
          return map[type][0]
        } else {
          return map[type][1]
        }
      } else {
        return ''
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.aside-bar {
  @include flex-col;
  flex-shrink: 0;
  width: 264px;
  height: 100%;

  :deep(.y-header) {
    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }
}
</style>
