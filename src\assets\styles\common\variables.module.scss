// 系列样式前缀
$namespace: y;

// 主题色
$themeColor: #0555CE;
$themeColor-dark: #0446A7; //focus
$themeColor-light: #4480DA; //hover

// 背景颜色
$bgColor: #fff;
$bgColor-dark: #F2F4F7;

// 边框颜色
$borderColor: #E8E8E8;

// 字体颜色
$txtColor: #262626;
$txtColor-light: #868686;
$txtColor-slight: #c5c5c5;
$txtColor-reverse: #fff;

// 提示颜色
$color-success: #52C41A;
$color-warning: #FA9904;
$color-danger: #F03838;
$color-info: #909399;
$color-delay: #22B8CF;


:export {
  themeColor: $themeColor;
  namespace: $namespace;
}