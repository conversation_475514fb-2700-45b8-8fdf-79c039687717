@use './mixin.scss' as *;

// y系列公共样式
@import './preset-style.scss';
// Element样式
@import './element-variables.scss';
// Element覆盖样式
@import './resetElement.scss';

// 全局公共样式
[v-cloak] {
  display: none !important;
}

* {
  box-sizing: border-box;
  margin: 0px;
  padding: 0px;
}

html {
  // font-size: 10px;
  line-height: 1.5;
  height: 100%;
}

body {
  font-size: 14px;
  height: 100%;
  color: $txtColor;
  overflow: hidden;
}

body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", Tahoma, Arial, sans-serif;
}

a {
  text-decoration: none;
}

/* h1,h2,h3,h4,h5,h6{font-weight: 400;} */
hr {
  background-color: #ddd;
  height: 1px;
  border: none;
}

input,
select {
  outline: 0;
}

ul {
  list-style: none;
}

#app {
  @include full;
  overflow: hidden;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: #E8E8E8;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #c5c5c5;
}

// @supports (scrollbar-width: thin) {
//   * {
//     scrollbar-width: thin;
//     scrollbar-color: #E8E8E8 transparent;
//   }
// }

.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
}


// 过渡样式
/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}