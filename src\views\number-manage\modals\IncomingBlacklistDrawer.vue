<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="120px">
        <el-form-item
          label="呼入黑名单号码"
          prop="numbers">
          <el-input
            v-model="form.blackPhoneNums"
            type="textarea"
            placeholder="请输入"
            :rows="20"
            clearable
            :disabled="data"
            :style="data ? '' : 'margin-bottom: -64px'" />
          <div
            v-if="!data"
            class="y-bar"
            style="position: relative">
            <span class="y-prefixer">多个号码请以顿号间隔</span>

            <el-upload
              action=""
              :auto-upload="false"
              accept=".xlsx,.xls"
              :on-change="handleImportNumbers"
              :multiple="false"
              :show-file-list="false"
              :limit="1">
              <el-button
                type="primary"
                plain
                size="small">
                <SvgIcon icon="go-to" />
                导入黑名单号码
              </el-button>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item
          label="生效时间"
          prop="effectiveTime">
          <el-date-picker
            v-model="form.effectiveTime"
            type="date"
            placeholder="请选择生效时间"
            style="width: 100%"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>

        <el-form-item
          label="失效时间"
          prop="expiryTime">
          <el-date-picker
            v-model="form.expiryTime"
            type="date"
            placeholder="请选择失效时间"
            style="width: 100%"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>

        <el-form-item
          v-if="!data"
          label="备注"
          prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入"
            :rows="14"
            clearable />
        </el-form-item>
      </el-form>

      <div class="y-footer">
        <el-button
          type="primary"
          size="small"
          plain
          @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          size="small"
          :loading="loading"
          @click="handleSubmit">
          提交申请
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addBlacklist, updateBlacklist, importPhoneNumber } from '@/api/number-manage'

export default {
  name: 'IncomingBlacklistDrawer',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '呼入黑名单配置申请',
    },
    selection: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    const validateNumbers = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入号码'))
      } else {
        // 简单验证每行是否是有效的电话号码格式
        const lines = value.split(',').filter((line) => line.trim())
        const phoneReg = /^[\d-\s()]+$/
        const invalidLines = lines.filter((line) => !phoneReg.test(line.trim()))
        if (invalidLines.length > 0) {
          callback(new Error('请输入正确的号码格式'))
        }
        callback()
      }
    }

    const validateDateRange = (rule, value, callback) => {
      if (value && this.form.effectiveTime && this.form.expiryTime) {
        if (new Date(this.form.expiryTime) <= new Date(this.form.effectiveTime)) {
          callback(new Error('失效时间必须晚于生效时间'))
        }
      }
      callback()
    }

    return {
      loading: false,
      form: {
        blackPhoneNums: '',
        effectiveTime: '',
        expiryTime: '',
        remark: '',
      },
      rules: {
        blackPhoneNums: [{ required: true, validator: validateNumbers, trigger: 'blur' }],
        effectiveTime: [
          { required: true, message: '请选择生效时间', trigger: 'change' },
          { validator: validateDateRange, trigger: 'change' },
        ],
        expiryTime: [
          { required: true, message: '请选择失效时间', trigger: 'change' },
          { validator: validateDateRange, trigger: 'change' },
        ],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler(data) {
        if (data) {
          if (Array.isArray(data)) {
            this.form = {
              phoneId: this.selection.join(),
              blackPhoneNums: data.map((item) => item.blacklistNumber).join(','),
              effectiveTime: '',
              expiryTime: '',
              remark: '',
            }
          } else {
            this.form = {
              phoneId: this.selection.join(),
              blackPhoneNums: data.blacklistNumber,
              effectiveTime: data.effectiveTime,
              expiryTime: data.expiryTime,
              remark: '',
            }
          }
        } else {
          this.reset()
        }
      },
      immediate: true,
    },
  },
  methods: {
    reset() {
      this.form = {
        blackPhoneNums: '',
        effectiveTime: '',
        expiryTime: '',
        remark: '',
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleImportNumbers(file) {
      // 添加loading状态
      const loading = this.$loading({
        lock: true,
        text: '正在导入号码...',
        spinner: 'el-icon-loading',
      })

      // 调用导入接口
      const [err, res] = await importPhoneNumber(file.raw)

      if (res && res.data) {
        // 导入成功，将号码填充到表单
        const importedNumbers = res.data
        if (importedNumbers.length > 0) {
          // 合并现有号码和新导入的号码
          let existingNumbers = []
          if (this.form.blackPhoneNums) {
            existingNumbers = this.form.blackPhoneNums.split(',').filter((num) => num.trim())
          }

          // 去重并合并
          const allNumbers = [...new Set([...existingNumbers, ...importedNumbers])]
          this.form.blackPhoneNums = allNumbers.join(',')

          this.$message.success(`成功导入 ${importedNumbers.length} 个号码`)
        }
      }
      loading.close()
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true

      const phoneNumbers = this.form.blackPhoneNums.split(',').filter((line) => line.trim())
      const submitData = {
        ...this.form,
        blackPhoneNums: phoneNumbers.join(),
        phoneId: this.selection.join(),
      }

      let api = this.data ? updateBlacklist : addBlacklist
      const [err, res] = await addBlacklist(submitData)

      if (res) {
        this.$message({
          message: '提交成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
