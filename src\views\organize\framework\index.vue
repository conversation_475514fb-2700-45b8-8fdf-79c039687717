<template>
  <div class="y-page y-container no-padding" id="framework" v-loading="loading">
    <AsideBar ref="asideBar" @set-current="setCurrent($event)" @add-dept="addDept($event)" />
    <base-card class="right-panel y-container no-padding">
      <div class="y-container no-padding">
        <div class="y-header">
          <h2 class="y-title">部门信息</h2>
        </div>
        <div class="pt-6 pb-0 flex-1 overflow-hidden relative flex flex-col">
          <empty-wrapper
            :toggle="deptId == '0'">
            <el-form class="flex-1 overflow-auto px-6" :model="form" ref="form" :rules="rules" label-width="116px" :inline="false" size="normal">
              <el-row :gutter="0">
                <el-col :span="12">
                  <el-form-item label="单位名称" prop="deptName">
                    <el-input v-model="form.deptName" placeholder="请输入部门名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="地址" prop="address">
                    <el-input v-model="form.address" placeholder="请输入地址" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-col :span="12">
                  <el-form-item label="负责人姓名" prop="responsibleName">
                    <el-input v-model="form.responsibleName" placeholder="请输入负责人姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人联系方式" prop="responsibleNumber">
                    <el-input v-model="form.responsibleNumber" placeholder="请输入负责人联系方式" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="0">
                <el-col :span="24">
                  <el-form-item label="审核类型">
                    <div class="msg p-4 mb-4">
                      1. 本单位审核：用户操作提交后，需要当前单位审核员角色或单位管理员角色的用户进行审核，审核通过后生效。 <br>
                      2. 本单位审核+市级单位审核：用户操作提交后，需要当前单位审核员角色或单位管理员角色的用户进行审核，审核通过后发送市级单位的审核员或单位管理员再次进行审核，市级单位审核通过后才生效。 <br>
                      3.  不审核：用户操作不审核，直接生效。
                    </div>
                    <div class="grid grid-cols-4 gap-4 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
                      <div class="box border-1 border-solid border-[#E0E0E0] rounded p-4" v-for="item in auditConfigList" :key="item.auditConfigId">
                        <div class="color-[#262626] font-bold text-sm leading-[22px] mb-4 relative z-10">
                          {{ auditConfig[item.auditType]['title'] }}
                          <el-popover
                            placement="bottom"
                            title=""
                            width="382"
                            trigger="hover"
                            popper-class="msg-popover"
                            content="">
                            <div class="text-xs leading-[18px] color-[#262626] mb-2">
                              {{ auditConfig[item.auditType]['tip'] }}
                            </div>
                            <div class="warn">
                              <i class="el-icon-warning-outline"></i>
                              仅针对业务管理员角色和单位管理员角色开放编辑权限
                            </div>
                            <i class="el-icon-question" slot="reference"></i>
                          </el-popover>
                        </div>
                        <el-radio-group v-model="item.auditFlowType">
                          <el-radio :label="1">本单位审核</el-radio>
                          <el-radio :label="2">本单位审核+市级单位审核</el-radio>
                          <el-radio :label="3">不审核</el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </empty-wrapper>
          <div class="h-[72px] flex items-center justify-end bg-white px-6 border-t border-x-0 border-b-0 border-solid border-[#E8E8E8]" v-show="deptId != '0'">
            <el-button type="primary" size="normal" class="px-10 p-y-3" @click="onSubmit">保存</el-button>
          </div>
        </div>
      </div>
    </base-card>
    <AddDept ref="addDept" :dept-id="deptId" :visible.sync="addDeptVisible" @success="fetchData" />
  </div>
</template>

<script>
import AsideBar from '../components/FrameWorkAside.vue'
import AddDept from '../components/AddDept.vue'

import { getDeptInfo, deptEdit } from '@/api/org'

export default {
  name: 'Framework',
  components: {
    AsideBar,
    AddDept
  },
  data() {
    return {
      title: '组织架构管理',
      addDeptVisible: false,
      form: {
        deptName: '',
        address: '',
        responsibleName: '',
        responsibleNumber: ''
      },
      rules: {
        deptName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
        ]
      },
      deptId: '0',
      loading: false,
      auditConfigList: [],
      auditConfig: {
        1: {
          title: '号码操作审核类型',
          tip: '用于控制号码的新增（进入）、删除（退出）、导入（批量进入）、导出等基本操作的审核流程类型。',
        },
        2: {
          title: '工作时间操作审核类型',
          tip: '用于控制号码的工作时间设置、批量设置操作的审核流程类型。',
        },
        3: {
          title: '欢迎语操作审核类型',
          tip: '用于控制号码的欢迎语设置、批量设置、删除、批量删除操作的审核流程类型。',
        },
        4: {
          title: '遇忙提示操作审核类型',
          tip: '用于控制号码的遇忙提示设置、批量设置、删除、批量删除操作的审核流程类型。',
        },
        5: {
          title: '非工作时间提示操作审核类型',
          tip: '用于控制号码的非工作时间提示设置、批量设置、删除、批量删除操作的审核流程类型。',
        },
        6: {
          title: '满意度评价开关操作审核类型',
          tip: '用于控制号码的满意度评价开启、关闭、批量开启、批量关闭操作的审核流程类型。',
        },
        7: {
          title: '录音白名单操作审核类型',
          tip: '用于控制号码的录音白名单的添加、删除、批量删除操作的审核流程类型。',
        },
        8: {
          title: '呼入黑名单操作审核类型',
          tip: '用于控制号码的呼入黑名单的添加、删除、批量删除、配置生失效时间操作的审核流程类型。',
        },
        9: {
          title: '协同号码操作审核类型',
          tip: '用于控制号码配置协同号码、删除协同号码操作的审核流程类型。',
        },
        10: {
          title: '通话并发数操作审核类型',
          tip: '用于控制号码通话并发数量配置操作的审核流程类型。',
        }
      }
    }
  },
  methods: {
    setCurrent(data) {
      this.deptId = data ? data.deptId : '0'
      if (this.deptId != '0') this.getDeptInfo()
    },
    addDept(data) {
      this.deptId = data ? data.deptId : '0'
      this.addDeptVisible = true
    },
    fetchData() {
      this.$refs.asideBar.fetchData()
    },
    async getDeptInfo() {
      this.loading = true
      const [err, res] = await getDeptInfo({deptId: this.deptId})
      if (res) {
        this.form = Object.assign({}, res.data)
        this.auditConfigList = res.auditConfigList || []
      }
      this.loading = false
    },
    async onSubmit() {
      await this.$refs.form.validate()
      this.loading = true
      const [err, res] = await deptEdit({...this.form, auditConfigList: this.auditConfigList})
      if (res) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.getDeptInfo()
          },
        })
      }
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
#framework {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    @include card;
    border-right: 1px solid #E8E8E8;
  }
  .msg {
    border-radius: 4px;
    background: linear-gradient(270deg, rgba(5, 85, 206, 0) 0%, rgba(5, 85, 206, 0.1) 100%);
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    color: #0555CE;
  }
  .box {
    position: relative;
    background: linear-gradient(180deg, rgba(5, 85, 206, 0.1) 1%, rgba(5, 85, 206, 0) 100%);
    background-size: 100% 48px;
    background-repeat: no-repeat;
    .el-radio {
      margin-bottom: 16px;
      line-height: 22px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 128px;
      height: 128px;
      background-size: 100% 100%;
    }
    &:nth-child(1) {
      &::after {
        background: url('~@/assets/images/organize/1.png') no-repeat center center;
      }
    }
    &:nth-child(2) {
      &::after {
        background: url('~@/assets/images/organize/2.png') no-repeat center center;
      }
    }
    &:nth-child(3) {
      &::after {
        background: url('~@/assets/images/organize/3.png') no-repeat center center;
      }
    }
    &:nth-child(4) {
      &::after {
        background: url('~@/assets/images/organize/4.png') no-repeat center center;
      }
    }
    &:nth-child(5) {
      &::after {
        background: url('~@/assets/images/organize/5.png') no-repeat center center;
      }
    }
    &:nth-child(6) {
      &::after {
        background: url('~@/assets/images/organize/6.png') no-repeat center center;
      }
    }
    &:nth-child(7) {
      &::after {
        background: url('~@/assets/images/organize/7.png') no-repeat center center;
      }
    }
    &:nth-child(8) {
      &::after {
        background: url('~@/assets/images/organize/8.png') no-repeat center center;
      }
    }
    &:nth-child(9) {
      &::after {
        background: url('~@/assets/images/organize/9.png') no-repeat center center;
      }
    }
    &:nth-child(10) {
      &::after {
        background: url('~@/assets/images/organize/10.png') no-repeat center center;
      }
    }
  }
}
</style>
<style lang="scss">
.msg-popover {
  padding: 16px;
  .warn {
    display: flex;
    align-items: center;
    height: 34px;
    border-radius: 4px;
    background: linear-gradient(270deg, rgba(250, 153, 4, 0) 0%, rgba(250, 153, 4, 0.1) 100%);
    padding: 0 8px;
    font-size: 12px;
    color: #FA9904;
    i {
      margin-right: 4px;
    }
  }
}
</style>