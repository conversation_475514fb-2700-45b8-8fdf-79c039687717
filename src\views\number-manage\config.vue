<template>
  <div
    id="number-config"
    class="y-page y-container no-padding"
    v-loading="loading">
    <div
      v-if="data"
      class="y-header">
      <el-image
        :src="require('@/assets/images/card-icon.svg')"
        fit="contain"
        style="width: 82px; height: 82px"></el-image>
      <div class="info">
        <h3 class="y-title">{{ data.phoneNumber || '暂无数据' }}</h3>
        <div class="body">
          <p class="y-item mb-1">
            <span class="label">号码类型：</span>
            <el-tag
              :type="numberTypeDict[data.numberType]?.type || 'info'"
              size="mini">
              {{ numberTypeDict[data.numberType]?.label || '未知' }}
            </el-tag>
          </p>
          <div class="flex gap-6">
            <p class="y-item">
              <span class="label">所属单位：</span><span class="value">{{ data.groupName || '暂无数据' }}</span>
            </p>
            <p class="y-item">
              <span class="label">号码来源：</span><span class="value">{{ numberSourceDict[data.numberSource]?.label || '暂无数据' }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="y-container">
      <empty-wrapper
        class="y-container"
        :toggle="!data"
        v-loading="loading"
        description="号码来源是第三方的号码、不展示配置信息">
        <template #empty>
          <el-button
            type="primary"
            size="small"
            @click="$router.push('/number-manage/number-list')"
            >返回列表</el-button
          >
        </template>
        <template>
          <div class="y-bar no-padding mb-4">
            <h4 class="y-title">工作时间</h4>
            <el-button
              type="primary"
              plain
              size="small"
              @click="handleAddWorkTime">
              <svg-icon icon="add"></svg-icon>
              新增工作时间
            </el-button>
          </div>
          <empty-wrapper
            class="y-container no-padding flex-row overflow-x-auto gap-4"
            :toggle="!workTimeList.length"
            v-loading="workTimeLoading"
            image-size="0"
            style="flex: 0 0 220px">
            <work-time-card
              v-for="(item, index) in workTimeList"
              :key="index"
              :title="`工作时间配置${index + 1}`"
              :data="item"
              @delete="handleDeleteWorkTime"
              @edit="handleEditWorkTime"
              @status-change="handleWorkTimeStatusChange" />
          </empty-wrapper>
          <div class="y-bar no-padding mt-6 mb-4">
            <h4 class="y-title">临时延长工作时间</h4>
            <el-button
              type="primary"
              plain
              size="small"
              @click="handleAddTempExtendWorkTime">
              <svg-icon icon="add"></svg-icon>
              临时延长工作时间
            </el-button>
          </div>
          <empty-wrapper
            class="y-container no-padding flex-row overflow-x-auto gap-4"
            :toggle="!tempExtendWorkTimeList.length"
            v-loading="tempExtendWorkTimeLoading"
            image-size="0"
            style="flex: 0 0 120px">
            <extend-time-card
              v-for="(item, index) in tempExtendWorkTimeList"
              :key="index"
              :data="item" />
          </empty-wrapper>
          <div
            class="y-container no-padding flex-row-wrapper overflow-x-auto mt-6"
            style="flex: 0 0 max-content">
            <div
              v-for="(label, key) in recordMap"
              :key="key"
              class="y-container no-padding"
              style="flex: 1 0 350px">
              <h4 class="y-title mb-4">{{ label }}</h4>
              <record-card
                :data="records[key]"
                @add="handleAddRecording(key)"
                @edit="handleEditRecording(key, $event)"
                @clear="handleDeleteRecording" />
            </div>
            <div
              class="y-container no-padding"
              style="flex: 1 0 200px">
              <h4 class="y-title mb-4">满意度评价</h4>
              <satisfaction-card
                :data="data"
                @edit="handleEditSatisfaction" />
            </div>
          </div>
          <whitelist-table
            class="mt-6"
            :phone-id="id" />
          <blacklist-table
            class="mt-6"
            :phone-id="id" />
          <div
            class="y-container no-padding flex-row-wrapper mt-6"
            style="flex: 0 0 244px">
            <div
              class="y-container no-padding"
              style="flex: 1 0 600px">
              <h4 class="y-title mb-4">协同号码</h4>
              <cooperative-card
                :data="cooperativeNumber"
                @clear="handleClearCooperativeNumber"
                @edit="handleEditCooperativeNumber" />
            </div>
            <div
              class="y-container no-padding"
              style="flex: 1 0 600px">
              <h4 class="y-title mb-4">通话并发数</h4>
              <concurrent-card
                :data="data"
                @edit="handleEditConcurrentCalls" />
            </div>
          </div>
        </template>
      </empty-wrapper>
    </div>

    <work-time-config-drawer
      :visible.sync="workTimeConfigDrawerVisible"
      :selection="[id]"
      :data="workTimeConfigData"
      @success="fetchWorkTimeList" />
    <extend-time-dialog
      :visible.sync="extendTimeDialogVisible"
      :data="data"
      :selection="[id]"
      @success="fetchTempExtendWorkTimeList" />
    <cooperative-number-dialog
      :visible.sync="cooperativeNumberDialogVisible"
      :data="cooperativeNumber"
      :selection="[id]"
      @success="fetchCooperativeNumber" />
    <satisfaction-evaluation-dialog
      :visible.sync="satisfactionEvaluationDialogVisible"
      :data="data"
      :selection="[id]"
      @success="fetchData" />
    <concurrent-calls-dialog
      :visible.sync="concurrentCallsDialogVisible"
      :data="data"
      :selection="[id]"
      @success="fetchData" />
    <recording-upload-dialog
      :visible.sync="recordingUploadDialogVisible"
      :data="recordingUploadData"
      :selection="[id]"
      :type="recordingUploadType"
      @success="fetchRecordList" />
  </div>
</template>

<script>
import WorkTimeCard from './components/WorkTimeCard'
import ExtendTimeCard from './components/ExtendTimeCard'
import SatisfactionCard from './components/SatisfactionCard'
import ConcurrentCard from './components/ConcurrentCard'
import CooperativeCard from './components/CooperativeCard'
import RecordCard from './components/RecordCard'
import WhitelistTable from './components/WhitelistTable'
import BlacklistTable from './components/BlacklistTable'

import WorkTimeConfigDrawer from './modals/WorkTimeConfigDrawer'
import ExtendTimeDialog from './modals/ExtendTimeDialog'
import CooperativeNumberDialog from './modals/CooperativeNumberDialog'
import SatisfactionEvaluationDialog from './modals/SatisfactionEvaluationDialog'
import ConcurrentCallsDialog from './modals/ConcurrentCallsDialog'
import RecordingUploadDialog from './modals/RecordingUploadDialog'

import {
  getWorkTimeList,
  getTempExtendWorkTimeList,
  deleteWorkTime,
  updateWorkTime,
  getPhoneNumberById,
  getAudioFileList,
  clearCollaborativeNumber,
  getCollaborativeNumberByPhoneId,
  deleteAudioFile,
} from '@/api/number-manage'
import { numberTypeDict, numberSourceDict } from '@/utils/constant'

export default {
  name: 'NumberConfig',
  components: {
    WorkTimeCard,
    ExtendTimeCard,
    SatisfactionCard,
    ConcurrentCard,
    CooperativeCard,
    RecordCard,
    WhitelistTable,
    BlacklistTable,
    WorkTimeConfigDrawer,
    ExtendTimeDialog,
    CooperativeNumberDialog,
    SatisfactionEvaluationDialog,
    ConcurrentCallsDialog,
    RecordingUploadDialog,
  },
  data() {
    return {
      loading: false,
      data: null,
      workTimeList: [], // 工作时间列表
      workTimeLoading: false, // 工作时间加载状态
      tempExtendWorkTimeList: [], // 临时延长工作时间列表
      tempExtendWorkTimeLoading: false, // 临时延长工作时间加载状态
      cooperativeNumber: null, // 协同号码
      cooperativeNumberLoading: false, // 协同号码加载状态
      recordMap: {
        1: '欢迎语',
        2: '遇忙提示',
        3: '非工作时间提示',
      },
      recordList: [], // 录音列表
      recordLoading: false, // 录音加载状态
      // modal
      workTimeConfigDrawerVisible: false,
      workTimeConfigData: null,
      extendTimeDialogVisible: false,
      cooperativeNumberDialogVisible: false,
      satisfactionEvaluationDialogVisible: false,
      concurrentCallsDialogVisible: false,
      recordingUploadDialogVisible: false,
      recordingUploadType: '1',
      recordingUploadData: null,
      // 字典
      numberTypeDict,
      numberSourceDict,
    }
  },
  computed: {
    id() {
      return this.$route.params.id
    },
    records() {
      return Object.fromEntries(this.recordList.map((i) => [i.audioType, i]))
    },
  },
  watch: {
    id(id) {
      if (id) {
      }
    },
  },
  created() {
    this.fetchData()
    this.fetchWorkTimeList()
    this.fetchTempExtendWorkTimeList()
    this.fetchRecordList()
    this.fetchCooperativeNumber()
  },
  activated() {
    this.fetchData()
    this.fetchWorkTimeList()
    this.fetchTempExtendWorkTimeList()
    this.fetchRecordList()
    this.fetchCooperativeNumber()
  },
  methods: {
    async fetchData() {
      if (!this.id) return

      this.loading = true

      const [err, res] = await getPhoneNumberById({
        phoneId: this.id,
      })

      if (res) {
        this.data = res.data
      }

      this.loading = false
    },

    // 获取工作时间列表
    async fetchWorkTimeList() {
      if (!this.id) return

      this.workTimeLoading = true
      // 获取工作时间列表
      const [err, res] = await getWorkTimeList({
        phoneNumberId: this.id,
      })

      if (res) {
        this.workTimeList = res.data || []
      }
      this.workTimeLoading = false
    },

    // 获取临时延长工作时间列表
    async fetchTempExtendWorkTimeList() {
      if (!this.id) return

      this.tempExtendWorkTimeLoading = true
      // 获取临时延长工作时间列表
      const [err, res] = await getTempExtendWorkTimeList({
        phoneNumberId: this.id,
      })

      if (res) {
        this.tempExtendWorkTimeList = res.data || []
      }
      this.tempExtendWorkTimeLoading = false
    },

    // 获取临时延长工作时间列表
    async fetchRecordList() {
      if (!this.id) return

      this.recordLoading = true
      // 获取临时延长工作时间列表
      const [err, res] = await getAudioFileList({
        phoneId: this.id,
      })

      if (res) {
        this.recordList = res.data || []
      }
      this.recordLoading = false
    },

    // 获取协同号码
    async fetchCooperativeNumber() {
      if (!this.id) return

      this.cooperativeNumberLoading = true
      const [err, res] = await getCollaborativeNumberByPhoneId({
        phoneId: this.id,
      })

      if (res) {
        this.cooperativeNumber = res.data || {}
      }
      this.cooperativeNumberLoading = false
    },
    // 新增工作时间
    handleAddWorkTime() {
      this.workTimeConfigDrawerVisible = true
      this.workTimeConfigData = null
    },
    // 新增临时延长工作时间
    handleAddTempExtendWorkTime() {
      this.extendTimeDialogVisible = true
    },
    // 删除工作时间
    async handleDeleteWorkTime(item) {
      try {
        await this.$confirm('确定要删除该工作时间配置吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        // 调用删除API
        const [err, res] = await deleteWorkTime({
          workTimeId: item.id,
        })

        if (res) {
          this.$message.success('提交成功')
          // 重新获取工作时间列表
          this.fetchWorkTimeList()
        }
      } catch (error) {
        console.error('删除工作时间失败:', error)
      }
    },
    // 编辑工作时间
    handleEditWorkTime(item) {
      this.workTimeConfigDrawerVisible = true
      this.workTimeConfigData = JSON.parse(JSON.stringify(item))
    },
    // 更新工作时间状态
    async handleWorkTimeStatusChange(data) {
      // 调用更新API
      const [err, res] = await updateWorkTime({
        ...data,
        workTimeId: data.id,
        isEnabled: data.blacklistStatus,
      })

      if (res) {
        this.$message.success('提交成功')
      }
      this.fetchWorkTimeList()
    },
    // 编辑满意度评价
    handleEditSatisfaction() {
      this.satisfactionEvaluationDialogVisible = true
    },
    // 编辑通话并发数
    handleEditConcurrentCalls() {
      this.concurrentCallsDialogVisible = true
    },
    // 清空协同号码
    async handleClearCooperativeNumber() {
      await this.$confirm('确定要清空协同号码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      // 调用清空协同号码API
      const [err, res] = await clearCollaborativeNumber({
        phoneId: this.id,
        collaborativeNumId: this.cooperativeNumber.id,
      })

      if (res) {
        this.$message.success('清空成功')
        this.fetchCooperativeNumber()
      }
    },
    // 编辑协同号码
    handleEditCooperativeNumber() {
      this.cooperativeNumberDialogVisible = true
    },
    handleAddRecording(type) {
      this.recordingUploadDialogVisible = true
      this.recordingUploadType = type
    },
    handleEditRecording(type, data) {
      this.recordingUploadDialogVisible = true
      this.recordingUploadType = type
      this.recordingUploadData = data
    },
    async handleDeleteRecording(data) {
      await this.$confirm('确定要删除该录音吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      const [err, res] = await deleteAudioFile({
        audioId: data.id,
      })

      if (res) {
        this.$message.success('删除成功')
      }

      this.fetchRecordList()
    },
  },
}
</script>

<style lang="scss" scoped>
#number-config::v-deep {
  gap: 16px;
  height: unset;
  min-height: 100%;

  > .y-header {
    gap: 16px;
    border: none;
    border-radius: 4px;
    background: $bgColor linear-gradient(114deg, rgba(5, 85, 206, 0.1) 0%, rgba(5, 85, 206, 0) 20%);
  }

  > .y-container {
    @include card;
  }

  .flex-row-wrapper {
    @include flex-row(flex-start, center);
    gap: 16px;
  }

  .el-form {
    .el-form-item:last-child {
      margin-right: 0;
    }
  }

  .el-pagination__sizes {
    margin-right: 0;
  }
}
</style>
