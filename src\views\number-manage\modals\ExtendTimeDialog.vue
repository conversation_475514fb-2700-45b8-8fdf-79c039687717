<template>
  <el-dialog
    title="临时延长工作时间"
    width="700px"
    :visible.sync="visible"
    @close="handleClose">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="100px">
        <!-- 延迟时间选择 -->
        <el-form-item
          label="延迟时间"
          prop="extendMinutes">
          <MultiSwitch
            v-model="form.extendMinutes"
            :options="extendMinutesOptions">
          </MultiSwitch>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button
        type="primary"
        size="small"
        plain
        @click="handleClose"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        @click="handleSubmit">
        延长
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addTempExtendWorkTime } from '@/api/number-manage'

export default {
  name: 'ExtendTimeDialog',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '临时延长工作时间',
    },
    selection: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      extendMinutesOptions: {
        15: '15分钟',
        30: '30分钟',
        60: '60分钟',
        120: '90分钟',
        180: '120分钟',
      },
      form: {
        extendMinutes: 15,
      },
      rules: {
        // extendMinutes: [{ required: true, message: '请选择延迟时间', trigger: 'change' }],
      },
    }
  },
  computed: {},
  methods: {
    reset() {
      this.form = {
        extendMinutes: 15,
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true

      // 提交新增临时延长工作时间
      const submitData = {
        ...this.form,
        phoneNumIds: this.selection.join(),
      }

      const [err, res] = await addTempExtendWorkTime(submitData)

      if (res) {
        this.$message({
          message: '提交成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
