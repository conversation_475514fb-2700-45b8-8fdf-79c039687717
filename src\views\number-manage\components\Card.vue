<template>
  <base-card
    class="card-container"
    :class="{ selectable: selectable, active: active, disabled: disabled }"
    @click.native="handleClick">
    <div class="info">
      <div
        class="y-bar no-padding"
        style="justify-content: flex-start; gap: 8px">
        <h4 class="y-title">
          {{ data.phoneNumber }}
        </h4>
        <el-tag
          :type="numberTypeDict[data.numberType]?.type || 'info'"
          size="mini">
          {{ numberTypeDict[data.numberType]?.label || '未知' }}
        </el-tag>
      </div>
      <div class="body">
        <p class="y-item">
          <span class="label">所属单位：</span><span class="value">{{ data.groupName || '暂无数据' }}</span>
        </p>
        <p class="y-item">
          <span class="label">号码来源：</span><span class="value">{{ numberSourceDict[data.numberSource]?.label || '暂无数据' }}</span>
        </p>
        <template v-if="data.numberSource == 1">
          <p class="y-item">
            <span class="label">工作时间：</span>
            <span class="value">{{ formatWorkTime }}</span>
          </p>
          <p class="y-item">
            <span class="label">满意度评价：</span><span class="value">{{ switchStatusDict[data.satisfactionSwitch]?.label || '关闭' }}</span>
          </p>
          <p class="y-item">
            <span class="label">协同号码：</span>
            <span class="value">{{ formatCollaborativeNumbers }}</span>
          </p>
          <p class="y-item">
            <span class="label">通话并发数：</span><span class="value">{{ data.concurrentCallLimit || '暂无数据' }}</span>
          </p>
          <p class="y-item">
            <span class="label">欢迎语：</span>
            <record
              v-if="data.welcomeAudioFileId"
              :src="getAudioFilePath(data.welcomeAudioFileId)"
              lazy
              style="flex: 0 0 276px" />
            <span
              v-else
              class="value"
              >未设置</span
            >
          </p>
          <p class="y-item">
            <span class="label">遇忙提示：</span>
            <record
              v-if="data.busyAudioFileId"
              :src="getAudioFilePath(data.busyAudioFileId)"
              lazy
              style="flex: 0 0 276px" />
            <span
              v-else
              class="value"
              >未设置</span
            >
          </p>
          <p class="y-item">
            <span class="label">非工作时间提示：</span>
            <record
              v-if="data.noWorkAudioFileId"
              :src="getAudioFilePath(data.noWorkAudioFileId)"
              lazy
              style="flex: 0 0 276px" />
            <span
              v-else
              class="value"
              >未设置</span
            >
          </p>
        </template>
      </div>
    </div>
    <div class="btn-set">
      <el-button
        v-if="data.numberType != 1"
        type="danger"
        plain
        size="small"
        @click="$emit('delete')"
        >删除</el-button
      >
      <el-button
        type="primary"
        plain
        size="small"
        @click="$emit('edit')"
        >配置</el-button
      >
    </div>
  </base-card>
</template>

<script>
import Record from '@/components/Record'
import { formatWorkTime, formatCollaborativeNumbers } from '@/utils/format'
import { numberTypeDict, numberSourceDict, switchStatusDict } from '@/utils/constant'
import { getAudioFilePath } from '@/api/number-manage'

export default {
  components: {
    Record,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    selectable: {
      type: Boolean,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      numberTypeDict,
      numberSourceDict,
      switchStatusDict,
    }
  },
  computed: {
    formatWorkTime() {
      return formatWorkTime(this.data)
    },

    formatCollaborativeNumbers() {
      return formatCollaborativeNumbers(this.data)
    },
  },
  methods: {
    handleClick() {
      if (this.disabled || !this.selectable) return
      this.$emit('select', this.data)
    },
    getAudioFilePath,
  },
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/modules/info-card.scss';

// 自定义
.card-container {
  border: 1px solid transparent;

  .y-item {
    @include flex-row;
  }

  &.selectable {
    cursor: pointer;
  }

  &.active {
    border-color: $themeColor;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 16px;
      height: 16px;
      background: url('@/assets/images/corner-tick.svg') no-repeat center/cover;
    }
  }

  &.disabled {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 2;
      cursor: not-allowed;
    }
  }
}
</style>
