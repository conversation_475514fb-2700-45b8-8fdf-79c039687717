<template>
  <div
    id="number-list"
    class="y-page y-container no-padding">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <base-card class="right-panel y-container no-padding">
      <div class="y-container no-padding">
        <div class="y-header">
          <h2 class="y-title">号码管理列表</h2>
          <template v-if="isBatch">
            <p
              class="y-prefixer"
              style="margin-right: 16px">
              点击号码卡片进行选择，统签号码不可删除，号码来源为第三方呼叫中心的不可配置
            </p>
            <el-button
              type="primary"
              plain
              size="small"
              @click="cancelBatch">
              取消
            </el-button>
            <el-button
              type="primary"
              plain
              size="small"
              @click="handleExport">
              批量导出号码
            </el-button>
            <el-button
              :key="0"
              type="danger"
              plain
              size="small"
              :disabled="!selection.length"
              @click="handleDelete(null)">
              删除
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button
                type="primary"
                size="small"
                style="margin-left: 8px">
                配置
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="workTime">工作时间</el-dropdown-item>
                <el-dropdown-item command="extendTime">临时延长工作时间</el-dropdown-item>
                <el-dropdown-item command="greeting">欢迎语</el-dropdown-item>
                <el-dropdown-item command="busyPrompt">遇忙提示</el-dropdown-item>
                <el-dropdown-item command="nonWorkPrompt">非工作时间提示</el-dropdown-item>
                <el-dropdown-item command="satisfaction">满意度评价</el-dropdown-item>
                <el-dropdown-item command="whitelist">录音白名单</el-dropdown-item>
                <el-dropdown-item command="blacklist">呼入黑名单</el-dropdown-item>
                <el-dropdown-item command="cooperativeNumber">协同号码</el-dropdown-item>
                <el-dropdown-item command="concurrency">通话并发数</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button
              type="primary"
              plain
              size="small"
              @click="isBatch = true">
              <svg-icon icon="batch"></svg-icon>
              批量操作
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleAdd">
              <svg-icon icon="add"></svg-icon>
              新增号码
            </el-button>
          </template>
        </div>
        <search-form
          :expand="expandSearch"
          ref="searchForm"
          :model="searchForm"
          :rules="rules">
          <el-form-item
            class="btn-item"
            label-width="0px">
            <el-button
              type="primary"
              size="small"
              plain
              @click.native="resetSearch">
              <svg-icon icon="reset"></svg-icon>
              重置
            </el-button>
            <el-button
              @click="fetchData"
              type="primary"
              size="small">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
            <el-button
              class="expand-btn"
              :type="!expandSearch ? 'primary' : 'default'"
              :plain="!expandSearch"
              size="small"
              @click.native="expandSearch = !expandSearch">
              <svg-icon icon="filter"></svg-icon>
              高级筛选
            </el-button>
          </el-form-item>
          <el-form-item
            label="工作时间"
            prop="hasWorkTime">
            <el-select
              v-model="searchForm.hasWorkTime"
              clearable
              placeholder="请选择">
              <el-option
                v-for="item in whetherOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="号码来源"
            prop="numberSource">
            <el-select
              v-model="searchForm.numberSource"
              clearable
              placeholder="请选择">
              <el-option
                v-for="item in numberSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="号码类型"
            prop="numberType">
            <el-select
              v-model="searchForm.numberType"
              clearable
              placeholder="请选择">
              <el-option
                v-for="item in numberTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="号码"
            prop="number">
            <el-input
              v-model="searchForm.number"
              clearable
              placeholder="请输入"></el-input>
          </el-form-item>
          <template #extra>
            <el-form-item
              label="遇忙提醒"
              prop="hasBusyTip">
              <el-select
                v-model="searchForm.hasBusyTip"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in whetherOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="协同号码"
              prop="hasCollaboratePhone">
              <el-select
                v-model="searchForm.hasCollaboratePhone"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in whetherOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="欢迎语"
              prop="hasWelcomeWord">
              <el-select
                v-model="searchForm.hasWelcomeWord"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in whetherOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="满意度评价开关"
              prop="hasSatisfactionEvaluation">
              <el-select
                v-model="searchForm.hasSatisfactionEvaluation"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in whetherOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="通话并发限制值"
              prop="concurrentCallLimit">
              <el-input-number
                v-model="searchForm.concurrentCallLimit"
                :min="1"
                controls-position="right"
                clearable
                placeholder="请输入"></el-input-number>
            </el-form-item>
            <el-form-item
              label="非工作时间提醒"
              prop="hasNoWorkTimeTip">
              <el-select
                v-model="searchForm.hasNoWorkTimeTip"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in whetherOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </template>
        </search-form>
      </div>
      <empty-wrapper
        class="y-container--tight"
        :toggle="!list || list.length === 0"
        v-loading="loading">
        <div class="y-card-wrapper y-container--tight no-padding">
          <Card
            v-for="(item, index) in list"
            :key="index"
            :data="item"
            :selectable="isBatch"
            :active="selection.includes(item.id)"
            @select="handleSelect(item)"
            @delete="handleDelete(item)"
            @edit="handleOperation(item)"></Card>
        </div>
      </empty-wrapper>
      <!-- <div class="y-footer">
        <pagination
          :current-page.sync="formData.pageNum"
          :page-size.sync="formData.pageSize"
          :total="total"
          @page="fetchData"></pagination>
      </div> -->
    </base-card>
    <add-number-drawer
      :visible.sync="addNumberDrawerVisible"
      :groupId="searchForm.groupId"
      :selection="selection"
      @success="handleSuccess" />
    <work-time-config-drawer
      :visible.sync="workTimeConfigDrawerVisible"
      :selection="selection"
      @success="handleSuccess" />
    <extend-time-dialog
      :visible.sync="extendTimeDialogVisible"
      :selection="selection"
      @success="handleSuccess" />
    <recording-whitelist-drawer
      :visible.sync="recordingWhitelistDrawerVisible"
      :selection="selection"
      @success="handleSuccess" />
    <incoming-blacklist-drawer
      :visible.sync="incomingBlacklistDrawerVisible"
      :selection="selection"
      @success="handleSuccess" />
    <cooperative-number-dialog
      :visible.sync="cooperativeNumberDialogVisible"
      :selection="selection"
      @success="handleSuccess" />
    <satisfaction-evaluation-dialog
      :visible.sync="satisfactionEvaluationDialogVisible"
      :selection="selection"
      @success="handleSuccess" />
    <concurrent-calls-dialog
      :visible.sync="concurrentCallsDialogVisible"
      :selection="selection"
      @success="handleSuccess" />
    <recording-upload-dialog
      :visible.sync="recordingUploadDialogVisible"
      :selection="selection"
      :type="recordingUploadType"
      @success="handleSuccess" />
  </div>
</template>

<script>
import AsideBar from '@/components/OrgTreePanel'
import Card from './components/Card'
import AddNumberDrawer from './modals/AddNumberDrawer'
import RecordingWhitelistDrawer from './modals/RecordingWhitelistDrawer'
import IncomingBlacklistDrawer from './modals/IncomingBlacklistDrawer'
import WorkTimeConfigDrawer from './modals/WorkTimeConfigDrawer'
import ExtendTimeDialog from './modals/ExtendTimeDialog'
import CooperativeNumberDialog from './modals/CooperativeNumberDialog'
import SatisfactionEvaluationDialog from './modals/SatisfactionEvaluationDialog'
import ConcurrentCallsDialog from './modals/ConcurrentCallsDialog'
import RecordingUploadDialog from './modals/RecordingUploadDialog'

import { getPhoneNumberList, deletePhoneNumber, exportPhoneNumber } from '@/api/number-manage'

export default {
  name: 'NumberList',
  components: {
    AsideBar,
    Card,
    AddNumberDrawer,
    RecordingWhitelistDrawer,
    IncomingBlacklistDrawer,
    WorkTimeConfigDrawer,
    ExtendTimeDialog,
    CooperativeNumberDialog,
    SatisfactionEvaluationDialog,
    ConcurrentCallsDialog,
    RecordingUploadDialog,
  },
  data() {
    return {
      loading: false,
      expandSearch: false,
      searchForm: {
        groupId: '',
        numberSource: '',
        numberType: '',
        number: '',
        hasWorkTime: null,
        hasWelcomeWord: null,
        hasBusyTip: null,
        hasNoWorkTimeTip: null,
        hasSatisfactionEvaluation: null,
        hasCollaboratePhone: null,
        concurrentCallLimit: null,
      },
      isBatch: false,
      rules: {},
      formData: {
        pageNum: 1,
        pageSize: 999,
      },
      list: [],
      total: 0,
      selection: [],
      addNumberDrawerVisible: false,
      recordingWhitelistDrawerVisible: false,
      incomingBlacklistDrawerVisible: false,
      workTimeConfigDrawerVisible: false,
      extendTimeDialogVisible: false,
      cooperativeNumberDialogVisible: false,
      satisfactionEvaluationDialogVisible: false,
      concurrentCallsDialogVisible: false,
      recordingUploadDialogVisible: false,
      recordingUploadType: '1',
      recordingUploadData: null,
      whetherOptions: [
        {
          label: '全部',
          value: null,
        },
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
      numberSourceOptions: [
        {
          label: '全部',
          value: '',
        },
        {
          label: 'AS平台',
          value: 1,
        },
        {
          label: '第三方呼叫中心',
          value: 2,
        },
      ],
      numberTypeOptions: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '统签',
          value: 1,
        },
        {
          label: '散号',
          value: 2,
        },
      ],
    }
  },
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        ...this.searchForm,
        ...this.formData,
      }

      const [err, res] = await getPhoneNumberList(payload)
      if (res) {
        this.list = res.data || []
        this.total = res.totalRow || 0
      }
      this.loading = false
    },
    resetSearch() {
      this.$refs.searchForm?.$refs?.searchForm?.resetFields()
      this.formData = {
        pageSize: 15,
        pageNum: 1,
      }
      this.fetchData()
    },
    handleAdd() {
      this.addNumberDrawerVisible = true
    },
    handleOperation(row) {
      this.$router.push({
        path: '/number-manage/number-config/' + row.id,
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        this.loading = true
        const [err, res] = await deletePhoneNumber({ phoneNumberIds: row ? row.id : this.selection.join() })
        if (res) {
          this.$message({
            message: '提交成功',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.fetchData()
            },
          })
        }

        this.loading = false
      } catch (e) {}
    },
    async handleExport() {
      const payload = {
        ...this.searchForm,
      }

      // 显示正在导出的loading消息
      const loadingMessage = this.$message({
        message: '正在导出...',
        duration: 0, // 不自动关闭
        type: 'info',
        iconClass: 'el-icon-loading',
      })

      const [err, res] = await exportPhoneNumber(payload)

      // 关闭loading消息
      loadingMessage.close()

      if (res) {
        this.$message.success('导出任务已创建，请稍后查看下载任务')
      }
    },
    setCurrent(id) {
      this.searchForm.groupId = id
      this.fetchData()
    },
    handleSelect(item) {
      if (this.selection.includes(item.id)) {
        this.selection = this.selection.filter((i) => i !== item.id)
      } else {
        this.selection.push(item.id)
      }
    },
    cancelBatch() {
      this.isBatch = false
      this.selection = []
    },
    handleCommand(command) {
      if (this.selection.length === 0) {
        this.$message.warning('请选择号码')
        return
      }

      if (command === 'whitelist') {
        this.recordingWhitelistDrawerVisible = true
      } else if (command === 'blacklist') {
        this.incomingBlacklistDrawerVisible = true
      } else if (command === 'workTime') {
        this.workTimeConfigDrawerVisible = true
      } else if (command === 'extendTime') {
        this.extendTimeDialogVisible = true
      } else if (command === 'cooperativeNumber') {
        this.cooperativeNumberDialogVisible = true
      } else if (command === 'satisfaction') {
        this.satisfactionEvaluationDialogVisible = true
      } else if (command === 'concurrency') {
        this.concurrentCallsDialogVisible = true
      } else if (command === 'greeting') {
        this.recordingUploadType = '1'
        this.recordingUploadDialogVisible = true
      } else if (command === 'busyPrompt') {
        this.recordingUploadType = '2'
        this.recordingUploadDialogVisible = true
      } else if (command === 'nonWorkPrompt') {
        this.recordingUploadType = '3'
        this.recordingUploadDialogVisible = true
      } 
    },
    handleSuccess() {
      this.cancelBatch()
      this.fetchData()
    },
  },
}
</script>

<style lang="scss" scoped>
#number-list {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    @include card;
    margin-right: 16px;
  }

  .right-panel {
    @import '@/assets/styles/modules/card-page.scss';
    background-color: transparent;

    .y-card-wrapper {
      @include card-wrapper(null, 900px); // padding, width, height（default: auto）
    }
  }
}
</style>
