<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    :size="isAdmin ? '700px' : '980px'"
    append-to-body>
      <div class="y-container no-padding" v-loading="loading">
        <div class="info m-4" v-show="data.ROLE_DESC">
          <img src="~@/assets/images/organize/user.png" alt="" class="mr-1">
          角色描述：{{ data.ROLE_DESC }}
        </div>
        <div class="border-[#E0E0E0] border border-solid rounded m-4 flex flex-1">
          <div class="w-[30%]" v-if="!isAdmin">
            <class-tree
              ref="tree"
              :tree="menuList"
              id-prop="deptId"
              :active-item="deptId"
              highlight-current
              @set-current="setCurrent($event)">
              <template #default="{ node, data }">
                <svg-icon
                  v-show="getNodeIcon(node, 'icon')"
                  :class="['node-icon', getNodeIcon(node, 'icon') ? 'fill' : '']"
                  :icon="getNodeIcon(node, 'icon')"></svg-icon>
                <span class="node-name">{{ data.deptName }}</span>
              </template>
            </class-tree>
          </div>
          <div class="w-[1px] bg-[#E8E8E8] h-full" v-if="!isAdmin"></div>
          <div class="w-[40%] h-full flex flex-col px-4 pt-4" :class="[isAdmin ? 'w-[40%]' : 'w-[30%]']">
            <el-input v-model="keyword" placeholder="请输入关键字" size="small"></el-input>
            <div class="flex-1 overflow-hidden">
              <el-tree
                ref="userTree"
                :data="users"
                :props="{}"
                node-key="userId"
                :default-expand-all="true"
                show-checkbox
                @check="checkUser">
              </el-tree>
            </div>
          </div>
          <div class="w-[1px] bg-[#E8E8E8] h-full"></div>
          <div class="flex-1 overflow-hidden h-full px-6 py-4 flex flex-col">
            <div class="flex items-center text-[#262626] font-bold mb-4">
              <div class="w-1 h-[14px] bg-[#0555CE] mr-2"></div>
              已选{{ select.length }}人
            </div>
            <div class="flex-1 overflow-auto flex flex-wrap content-start gap-2">
              <el-button
                v-for="item in select"
                :key="item.userId"
                type="primary"
                size="small"
                plain>
                {{ item.userName }}
                <i class="el-icon-close" @click="delUser(item)"></i>
              </el-button>
            </div>
          </div>
        </div>
        <div class="y-footer">
          <el-link
            type="primary"
            :underline="false"
            @click="handleReset">
            <svg-icon icon="reset"></svg-icon>
            重置
          </el-link>
          <div style="flex: 1"></div>
          <el-button
            type="primary"
            plain
            @click="handleClose"
            >取消</el-button
          >
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSubmit">
            确定
          </el-button>
        </div>
      </div>
  </el-drawer>
</template>

<script>
import ClassTree from '@/components/ClassTree'
import { getDeptUserList, getDeptTree, getRoleSelectedUser, editUser } from '@/api/org'
import { mergeAndDeduplicate } from '@/utils/index'

export default {
  name: 'MemberDrawer',
  components: {
    ClassTree,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    title() {
      return '添加' + this.data.ROLE_NAME + '成员'
    },
    isAdmin() {
      return this.data.ROLE_TYPE == '1'
    }
  },
  watch: {
    visible: {
      async handler(newVal) {
        if (newVal) {
          if (newVal.ROLE_TYPE == '1') {
            await this.getDeptUserList()
            await this.getRoleSelectedUser()
          } else {
            await this.getDeptTree()
            await this.getRoleSelectedUser()
          }
        }  
      },
      deep: true
    }
  },
  data() {
    return {
      loading: false,
      keyword: '',
      menuList: [],
      deptId: '',
      users: [],
      select: []
    }
  },
  methods: {
    getNodeIcon(node, type) {
      const map = {
        icon: ['minus-round', 'plus-round'],
      }
      if (node.childNodes.length > 0) {
        if (node.expanded) {
          return map[type][0]
        } else {
          return map[type][1]
        }
      } else {
        return ''
      }
    },
    setCurrent(data) {
      this.deptId = data.deptId
      this.users = [{label: data.deptName, userId: data.deptId, root: '0', children: []}]
      this.getDeptUserList()
    },
    reset() {
      this.$refs.userTree.setCheckedKeys([])
      this.getRoleSelectedUser()
    },
    handleClose() {
      this.keyword = ''
      this.menuList = []
      this.deptId = ''
      this.users = [],
      this.select = []
      this.$emit('update:visible', false)
    },
    handleReset() {
      this.reset()
    },
    async handleSubmit() {
      this.loading = true
      const [err, res] = await editUser({roleId: this.data.ROLE_ID, userIds: this.select.map(item => item.userId).join()})
      if (res) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }
      this.loading = false
    },
    async getDeptUserList() {
      this.loading = true
      let query = {
        pageType: '3',
        pageNo: 1,
        pageSize: 999,
        deptId: this.data.ROLE_TYPE == '1' ? '0' : this.deptId
      }
      const [err, res] = await getDeptUserList(query)
      if (res) {
        res.data.forEach(item => {
          item['label'] = item.userName
        })
        if (this.users.length) this.users[0].children = res.data
      }
      this.loading = false
    },
    async getDeptTree() {
      const [err, res] = await getDeptTree()
      if (res) {
        this.menuList = res.data || []
        if (this.menuList.length && this.menuList[0]?.children && this.menuList[0]?.children.length) {
          this.setCurrent(this.menuList[0].children[0])
        }
      }
    },
    async getRoleSelectedUser() {
      this.loading = true
      const [err, res] = await getRoleSelectedUser({roleId: this.data.ROLE_ID})
      if (res) {
        console.log(this.users)
        this.select = res.data || []
        if (this.select.length) {
          console.log(this.select.map(item => item.userId))
          this.$nextTick(() => {
            console.log(this.$refs.userTree)
            setTimeout(() => {
              this.$refs.userTree.setCheckedKeys(this.select.map(item => item.userId), true)
            }, 0);
          })
        }
      }
      this.loading = false
    },
    checkUser(data, checked) {
      console.log('checkUser', data, checked)
      const isSave = this.select.find(item => item.userId == data.userId)
      const isRoot = data.root == '0'
      let checkUser = this.$refs.userTree.getCheckedNodes(true, false).filter(item => typeof item.root == 'undefined')
      if (isRoot) {
        if (checked.checkedKeys.length) {
          this.select = mergeAndDeduplicate([this.select, checkUser], 'userId')
          console.log(this.select)
        } else {
          const childNodes = this.users[0].children.map(item => item.userId)
          this.select = this.select.filter(item => childNodes.indexOf(item.userId) == -1)
        }
      } else {
        if (isSave) {
          this.select = this.select.filter(item => item.userId != data.userId)
        } else {
          this.select = mergeAndDeduplicate([this.select, checkUser], 'userId')
        }
      }
      console.log(this.select)
    },
    delUser(data) {
      console.log(this.select.map(item => item.userId))
      this.select = this.select.filter(item => item.userId != data.userId)
      this.$refs.userTree.setCheckedKeys(this.select.map(item => item.userId), true)
    }
  }
}
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  align-items: center;
  height: 46px;
  border-radius: 4px;
  padding: 0 16px;
  color: #0555CE;
  font-size: 14px;
  background: linear-gradient(270deg, rgba(5, 85, 206, 0) 0%, rgba(5, 85, 206, 0.1) 100%);  
}
</style>