import store from '@/store'
import router, { constantRoutes } from './index.js'
import NProgress from 'nprogress'

import 'nprogress/nprogress.css'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = [] // no redirect whitelist

// 设置权限/角色
await store.dispatch('permission/setPermission')
// 挂载权限菜单

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  console.log('route-start', to, from)

  // 如果路由不存在，重新导航过去，注意：因为在tagBar里面重新添加了这个路由
  // if (to.path === '/404') {
  //   // 二次重新导航，说明路由真的不存在，导航到默认菜单
  //   if (from.path.startsWith('/redirect/')) {
  //     router.replace(firstMenu)
  //     return
  //   }
  //   // 重新导航
  //   router.replace({ path: '/redirect' + to.redirectedFrom })
  // }

  next()
})

router.afterEach(() => {
  NProgress.done()
  console.log('route-done')
})