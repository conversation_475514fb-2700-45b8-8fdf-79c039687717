<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="auto">
        <el-form-item>
          <div class="flex justify-center">
            <div class="flex flex-col items-center">
              <img src="~@/assets/images/organize/woman.png" alt="" v-show="form.SEX == '0'">
              <img src="~@/assets/images/organize/man.png" alt="" v-show="form.SEX == '1'">
              <el-button
                type="primary"
                size="small"
                class="mt-4">
                <svg-icon icon="upload"></svg-icon>
                上传头像
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          label="姓名"
          prop="AGENT_NAME">
          <el-input
            v-model="form.AGENT_NAME"
            placeholder="请输入"
            clearable />
        </el-form-item>

        <el-form-item
          label="登录账号"
          prop="USER_ACCT">
          <el-input
            v-model="form.USER_ACCT"
            placeholder="请输入"
            clearable />
        </el-form-item>

        <el-form-item
          label="所属单位"
          prop="DEPT_NAME">
          <el-input
            v-model="form.DEPT_NAME"
            placeholder="请选择"
            readonly
            @click.native="show = true" />
        </el-form-item>

        <el-form-item
          label="手机号"
          prop="MOBILE">
          <el-input
            v-model="form.MOBILE"
            placeholder="请输入"
            clearable />
        </el-form-item>

        <el-form-item
          label="角色"
          prop="ROLE_IDS">
          <el-select v-model="form.ROLE_IDS" placeholder="请选择" clearable filterable multiple>
            <el-option
              v-for="item in roles"
              :disabled="deptId != '0' && item.ROLE_TYPE == '1'"
              :key="item.ROLE_ID"
              :label="item.ROLE_NAME"
              :value="item.ROLE_ID">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="性别"
          prop="SEX">
          <div class="bg-[#F2F4F7] rounded inline-block h-[40px] px-4 leading-[38px] text-[#262626] mr-4 cursor-pointer" :class="['tag', form.SEX == value ? 'active' : '']" v-for="(label, value) in SEX" :key="value" @click="form.SEX = value">{{ label }}</div>
        </el-form-item>
        
        <el-form-item
          label="邮箱"
          prop="EMAIL">
          <el-input
            v-model="form.EMAIL"
            placeholder="请输入"
            clearable />
        </el-form-item>
      </el-form>
      <div class="y-footer">
        <div style="flex: 1"></div>
        <el-button
          type="primary"
          plain
          @click="handleClose"
          >取消</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit">
          确定
        </el-button>
      </div>
    </div>
    <SelectDept :visible.sync="show" :dept-id="form.DEPT_ID" @select="handleSelect" />
  </el-drawer>
</template>

<script>
import { getRoleList, userSave } from '@/api/org'
import SelectDept from './SelectDept'

export default {
  name: 'AddUser',
  components: {SelectDept},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    deptId: {
      type: [String, Number],
      default: null,
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    var checkEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入邮箱'));
      } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
        callback(new Error('请输入正确的邮箱格式'));
      } else {
        callback();
      }
    }
    return {
      loading: false,
      form: {
        AGENT_NAME: '',
        USER_ID: '',
        USER_ACCT: '',
        DEPT_ID: '',
        DEPT_NAME: '',
        MOBILE: '',
        ROLE_IDS: [],
        LOCK_STATE: '0',
        SEX: '1',
        EMAIL: ''
      },
      rules: {
        AGENT_NAME: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { max: 10, message: '长度不能超过10个字符', trigger: 'blur' },
        ],
        USER_ACCT: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
        DEPT_ID: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
        MOBILE: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        ROLE_IDS: [{ required: true, message: '请选择角色', trigger: 'change' }],
        SEX: [{ required: true, message: '请选择性别', trigger: 'blur' }],
        EMAIL: [{ required: true, validator: checkEmail, trigger: 'blur' }]
      },
      roles: [],
      SEX: {
        '1': '男',
        '0': '女'
      },
      show: false
    }
  },
  computed: {
    title() {
      return !this.form.USER_ID ? '新增用户' : '编辑用户'
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getRoleList()
        }
      },
      immediate: true,
    },
    data: {
      handler(val) {
        if (val) {
          this.form = Object.assign({}, val)
        }
      },
      deep: true
    }
  },
  methods: {
    async getRoleList() {
      const [err, res] = await getRoleList({pageType: '3', pageNo: 1, pageSize: 100})
      if (res) {
        this.roles = res.data
      }
    },
    reset() {
      this.form = {
        AGENT_NAME: '',
        USER_ID: '',
        USER_ACCT: '',
        DEPT_ID: '',
        DEPT_NAME: '',
        MOBILE: '',
        ROLE_IDS: [],
        LOCK_STATE: '0',
        SEX: '1',
        EMAIL: ''
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleReset() {
      this.reset()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true
      const [err, res] = await userSave({ ...this.form })

      if (res) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
    handleSelect(data) {
      console.log(data)
      this.form.DEPT_ID = data.deptId
      this.form.DEPT_NAME = data.deptName
    }
  },
}
</script>

<style lang="scss" scoped>
.tag {
  position: relative;
  border: 1px solid transparent;
  &.active {
    color: #0555CE;
    border: 1px solid #0555CE;
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      width: 16px;
      height: 16px;
      background: url('../../../assets/images/organize/check.png') center / 100% 100% no-repeat;
    }
  }
}
</style>
