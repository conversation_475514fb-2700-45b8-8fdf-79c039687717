<template>
  <div
    class="h-full overflow-y-auto relative"
    v-loading="loading"
    element-loading-text="加载中..."
    v-cloak
  >
    <div class="detail-info">
      <div
        class="state"
        :style="handleTabActiveContent('style', formData.flowStatus, 'value')"
      >
        {{ handleTabActiveContent("label", formData.flowStatus, "value") }}
      </div>

      <div class="flex">
        <img class="avatar" src="@/assets/images/my-apply/apply-avatar.png" />

        <div class="info">
          <div class="text-xl font-bold mb-2 phone">
            {{ formData.phoneNumber }}
          </div>

          <div class="info-detail">
            <div>
              <div class="label">业务类型：</div>
              <div
                class="type-value"
                :style="
                  handleBusinessTypeContent('style', formData.businessType)
                "
              >
                {{ handleBusinessTypeContent("label", formData.businessType) }}
              </div>
            </div>
            <div>
              <div class="label">申请编号：</div>
              <div>{{ formData.applicationNo }}</div>
            </div>
            <div>
              <div class="label">申请人：</div>
              <div>{{ formData.applicantName }}</div>
            </div>
            <div>
              <div class="label">申请时间：</div>
              <div>{{ formData.applicationTime }}</div>
            </div>
            <!-- 审核详情不显示 -->
            <div v-if="fromWhere !== 'auditList'">
              <div class="label">关闭状态：</div>
              <div class="blue">
                {{ handleCloseStatusContent(formData.closeStatus) }}
              </div>
            </div>
          </div>

          <div :class="['description', { '!mb-6': fromWhere === 'auditList' }]">
            <div class="circle"></div>
            <div>申请说明：</div>
            <div class="description-content">
              {{ formData.applicationDesc }}
            </div>
          </div>

          <!-- 审核详情不显示 -->
          <div class="flex mb-3" v-if="fromWhere !== 'auditList'">
            <div class="label mr-4 flow-label">审核进度</div>
            <div class="flow-list">
              <template v-for="(item, index) in flowList">
                <div
                  v-if="index != 0"
                  :class="[
                    'flow-line',
                    { 'line-active': handleIsLineActive(item, index) },
                  ]"
                ></div>
                <div
                  :class="[
                    'node',
                    {
                      'node-cancel': item.flowStatus == '3',
                      'node-done': item.flowStatus == '2',
                      'node-active': item.flowStatus == '1',
                    },
                  ]"
                >
                  <img
                    class="node-icon"
                    v-if="
                      item.flowStatus != '0' &&
                      !!handleFlowStatusIcon(item, index)
                    "
                    :src="handleFlowStatusIcon(item, index)"
                  />
                  <div>{{ item.currentNodeName }}</div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="more-info">
      <div>
        <div class="title">涉及号码</div>
        <InvolveNum :formData="formData" />
      </div>

      <div>
        <div class="title">操作信息</div>
        <OperationInfo :formData="formData" />
      </div>

      <div v-if="fromWhere === 'auditList' && formData.flowStatus == '1'">
        <div class="title">审核信息</div>
        <el-form
          class="audit-form"
          ref="auditForm"
          :inline="true"
          :model="auditForm"
          label-width="75px"
          size="small"
          :rules="auditRules"
        >
          <el-form-item label="处理描述" prop="approvalDesc">
            <el-input
              v-model="auditForm.approvalDesc"
              type="textarea"
              :rows="4"
              placeholder="请输入"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <div v-else>
        <div class="title">具体流程</div>

        <el-timeline class="timeline">
          <el-timeline-item
            v-for="(item, index) in approvalList"
            :key="item.id"
          >
            <div class="time-box">
              <div class="day">{{ item.day }}</div>
              <div>{{ item.yearMonth }}</div>
              <div class="time">{{ item.time }}</div>
            </div>

            <template #dot>
              <img
                class="dot"
                src="@/assets/images/my-apply/timeline-dot.png"
              />
            </template>

            <div class="approval-top">
              <div class="approval-title">
                {{ `${index + 1}.${item.stepName}` }}
              </div>

              <div>
                <span class="label">
                  {{ handleApprovalTimeLabel(item) }}：
                </span>
                <span class="text-sm">
                  {{ item.createTime }}
                </span>
              </div>
            </div>

            <div class="approval-content">
              <template v-if="item.approvalStatus == '1'">
                <div>
                  <div class="label">申请人：</div>
                  <div>{{ formData.applicantName }}</div>
                </div>
                <div>
                  <div class="label">申请编号：</div>
                  <div>{{ formData.applicationNo }}</div>
                </div>
                <div>
                  <div class="label">业务类型：</div>
                  <div
                    class="type-value"
                    :style="
                      handleBusinessTypeContent('style', formData.businessType)
                    "
                  >
                    {{
                      handleBusinessTypeContent("label", formData.businessType)
                    }}
                  </div>
                </div>
                <div class="desc">
                  <div class="label">申请说明：</div>
                  <div class="desc-value">
                    {{ formData.applicationDesc }}
                  </div>
                </div>
              </template>

              <template v-else-if="item.approvalStatus == '4'">
                <div class="desc">
                  <div class="label">关闭说明：</div>
                  <div class="desc-value">
                    {{ item.approvalDesc }}
                  </div>
                </div>
                <div>
                  <div class="label">关闭状态：</div>
                  <div
                    class="type-value"
                    :style="
                      statusColorList[item.approvalStatus]?.['style'] || ''
                    "
                  >
                    {{ statusColorList[item.approvalStatus]?.["label"] || "" }}
                  </div>
                </div>
              </template>

              <template v-else>
                <div>
                  <div class="label">处理单位：</div>
                  <div>{{ item.dealUnit }}</div>
                </div>
                <div>
                  <div class="label">处理人：</div>
                  <div>{{ item.approverName }}</div>
                </div>
                <div>
                  <div class="label">处理状态：</div>
                  <div
                    class="type-value"
                    :style="
                      statusColorList[item.approvalStatus]?.['style'] || ''
                    "
                  >
                    {{ statusColorList[item.approvalStatus]?.["label"] || "" }}
                  </div>
                </div>
                <div class="desc">
                  <div class="label">处理描述：</div>
                  <div class="desc-value">
                    {{ item.approvalDesc }}
                  </div>
                </div>
              </template>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <div class="btn-box">
      <el-button type="primary" plain @click="handleBack()">返回</el-button>

      <!-- 申请详情按钮 -->
      <template v-if="fromWhere === 'applyList'">
        <el-button
          v-if="formData.flowStatus == '1' || handleIsShowBtn"
          type="danger"
          plain
          :loading="btnLoading[0]"
          @click="handleCloseApply()"
        >
          关闭申请
        </el-button>
        <el-button
          v-if="handleIsShowBtn"
          type="primary"
          :loading="btnLoading[1]"
          @click="handleEditApply()"
        >
          修改申请
        </el-button>
      </template>

      <!-- 审核详情按钮 -->
      <template
        v-else-if="fromWhere === 'auditList' && formData.flowStatus == '1'"
      >
        <el-button
          type="danger"
          :loading="btnLoading[2]"
          @click="handleAudit('noPass', 2)"
        >
          审核不通过
        </el-button>
        <el-button
          type="success"
          :loading="btnLoading[3]"
          @click="handleAudit('pass', 3)"
        >
          审核通过
        </el-button>
      </template>
    </div>

    <component
      :is="configComponent"
      :visible.sync="configVisible"
      :data="configData"
      fromWhere="myApplyDetail"
      @success="getDetail()"
    >
    </component>
  </div>
</template>
<script>
import { auditApply } from "@/api/audit-manage.js";
import { closeApply, getApplyDetail } from "@/api/my-apply.js";
import ConcurrentCallsDialog from "@/views/number-manage/modals/ConcurrentCallsDialog.vue";
import CooperativeNumberDialog from "@/views/number-manage/modals/CooperativeNumberDialog.vue";
import SatisfactionEvaluationDialog from "@/views/number-manage/modals/SatisfactionEvaluationDialog.vue";
import WorkTimeConfigDrawer from "@/views/number-manage/modals/WorkTimeConfigDrawer.vue";
import applyMixins from "../mixins/index.js";
import InvolveNum from "./components/InvolveNum.vue";
import OperationInfo from "./components/OperationInfo.vue";

export default {
  name: "ApplyDetail",
  mixins: [applyMixins],
  components: {
    InvolveNum,
    OperationInfo,
    WorkTimeConfigDrawer,
    SatisfactionEvaluationDialog,
    CooperativeNumberDialog,
    ConcurrentCallsDialog,
  },
  data() {
    return {
      action: "",
      applicationId: "",
      loading: false,
      formData: {},
      approvalList: [], // approvalStatus：1-提交，2-审核通过，3-审核不通过，4-关闭
      flowList: [], // flowStatus：0-待执行 1-进行中，2-已完成，3-已取消
      btnLoading: [false, false, false, false],
      // 状态样式列表
      // 1-提交，2-审核通过，3-审核不通过，4-关闭
      statusColorList: {
        2: {
          label: "审核通过",
          style:
            "backgroundColor: rgba(82, 196, 26, 0.1);color: rgba(82, 196, 26, 1);",
        },
        3: {
          label: "审核不通过",
          style:
            "backgroundColor: rgba(240, 56, 56, 0.1);color:  rgba(240, 56, 56, 1);",
        },
        4: {
          label: "已关闭",
          style:
            "backgroundColor: rgba(38, 38, 38, 0.1);color: rgba(134, 134, 134, 1);",
        },
      },
      configComponent: null,
      configVisible: false,
      configData: {},
      fromWhere: "",
      auditForm: {
        approvalDesc: "",
      },
      auditRules: {
        approvalDesc: [
          { required: true, message: "请输入", trigger: ["blur", "change"] },
        ],
      },
    };
  },
  computed: {
    // 判断当前连接线是否激活
    handleIsLineActive() {
      return (item, index) => {
        const cancelIndex = this.flowList.findIndex(
          (flowItem) => flowItem.flowStatus == "3"
        );

        if (cancelIndex == -1) {
          return ["1", "2"].includes(item.flowStatus + "");
        } else {
          return cancelIndex >= index;
        }
      };
    },

    // 返回审核步骤中的图标
    handleFlowStatusIcon() {
      return (item, index) => {
        const cancelIndex = this.flowList.findIndex(
          (flowItem) => flowItem.flowStatus == "3"
        );

        let icon = "";
        switch (item.flowStatus + "") {
          case "1":
            icon = require("@/assets/images/my-apply/active-icon.png");
            break;
          case "2":
            icon =
              index == this.flowList.length - 1
                ? require("@/assets/images/my-apply/close-icon.png")
                : cancelIndex != -1 && cancelIndex < index
                ? ""
                : require("@/assets/images/my-apply/done-icon.png");
            break;
          case "3":
            icon = require("@/assets/images/my-apply/cancel-icon.png");
            break;
          default:
            break;
        }

        return icon;
      };
    },

    // 返回具体流程的时间标题
    handleApprovalTimeLabel() {
      return (item) => {
        if (!item.approvalStatus) return "";

        return item.approvalStatus == "1"
          ? "申请时间"
          : item.approvalStatus == "4"
          ? "关闭时间"
          : "处理时间";
      };
    },

    // 审核不通过状态下是否显示关闭申请和修改申请按钮
    handleIsShowBtn() {
      return (
        this.formData.flowStatus == "3" &&
        ["2", "3", "4", "5", "6", "9", "10"].includes(
          this.formData.businessType + ""
        )
      );
    },
  },
  mounted() {
    const { id, action, fromWhere, auditParams } = this.$route.query;
    this.applicationId = id;
    this.action = action;
    this.fromWhere = fromWhere;
    Object.assign(this.auditForm, JSON.parse(auditParams || "{}"));
    this.getDetail();
  },
  methods: {
    // 获取详情
    async getDetail() {
      try {
        this.loading = true;
        const [err, res] = await getApplyDetail(this.action, {
          applicationId: this.applicationId,
        });

        if (res?.data) {
          const { info, approvalList, flowList } = res.data;
          this.formData = info;
          this.approvalList = approvalList.map((item) => {
            if (item.createTime) {
              const timeList = item.createTime.split(" ");
              const dateList = timeList[0].split("-");
              return {
                ...item,
                day: dateList[2],
                yearMonth: dateList[0] + "-" + dateList[1],
                time: timeList[1],
              };
            } else {
              return item;
            }
          });
          this.flowList = flowList;
        }
      } finally {
        this.loading = false;
      }
    },

    // 返回
    handleBack() {
      const name = this.fromWhere === "auditList" ? "AuditList" : "ApplyList";
      this.$router.push({ name });
    },

    // 关闭申请
    handleCloseApply() {
      this.$confirm("确定关闭申请吗？", "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            this.btnLoading.splice(0, 1, true);
            const [err, res] = await closeApply({
              applicationId: this.applicationId,
            });
            if (res?.state == 1) {
              this.$message.success(res.msg);
              this.getDetail();
            }
          } finally {
            this.btnLoading.splice(0, 1, false);
          }
        })
        .catch(() => {});
    },

    // 修改申请
    handleEditApply() {
      const {
        businessType,
        workTime1Start,
        workTime1End,
        workTime2Start,
        workTime2End,
        workTime3Start,
        workTime3End,
        workDays,
        skipHoliday,
        isEnabled,
        switchStatus: satisfactionSwitch,
        collaborativeNumber1,
        collaborativeNumber2,
        phoneId,
        busiId,
        applicationNo,
        concurrentCallLimit,
      } = this.formData;

      switch (businessType + "") {
        // 工作时间
        case "2":
          this.configComponent = "WorkTimeConfigDrawer";
          this.configVisible = true;
          this.configData = {
            workTime1Start,
            workTime1End,
            workTime2Start,
            workTime2End,
            workTime3Start,
            workTime3End,
            workDays,
            skipHoliday,
            isEnabled,
            id: this.applicationId,
            phoneId,
            busiId,
          };
          break;

        // 欢迎语、遇忙提示、非工作时间提示
        case "3":
        case "4":
        case "5":
          break;

        // 满意度评价开关
        case "6":
          this.configComponent = "SatisfactionEvaluationDialog";
          this.configData = {
            satisfactionSwitch,
            applicationId: this.applicationId,
            phoneId,
            busiId,
          };
          break;

        // 协同号码
        case "9":
          this.configComponent = "CooperativeNumberDialog";
          this.configData = {
            collaborativeNumber1,
            collaborativeNumber2,
            applicationId: this.applicationId,
            phoneId,
            busiId,
            applicationNo,
          };
          break;

        // 通话并发数
        case "10":
          this.configComponent = "ConcurrentCallsDialog";
          this.configData = {
            concurrentCallLimit,
            applicationId: this.applicationId,
            phoneId,
            busiId,
            applicationNo,
          };
          break;

        default:
          break;
      }
      this.configVisible = true;
    },

    // 审核
    async handleAudit(type, btnNum) {
      await this.$refs.auditForm.validate();

      const msg = type === "pass" ? "审核通过" : "审核不通过";
      this.$confirm(`确定${msg}吗？`, "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            this.btnLoading.splice(btnNum, 1, true);
            const [err, res] = await auditApply({
              ...this.auditForm,
              auditStatus: type === "pass" ? 2 : 3,
            });

            if (res?.state == 1) {
              this.$message.success(res.msg);
              this.handleBack();
            }
          } finally {
            this.btnLoading.splice(btnNum, 1, false);
          }
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.detail-info {
  position: relative;
  overflow: hidden;
  padding: 24px 24px 0;
  border-radius: 4px;
  background: #fff
    linear-gradient(114deg, rgba(5, 85, 206, 0.1) 0%, rgba(5, 85, 206, 0) 20%);

  .state {
    position: absolute;
    top: 15px;
    right: -45px;
    padding: 5px 0;
    width: 150px;
    text-align: center;
    transform: rotate(45deg);
  }

  .avatar {
    margin-right: 16px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }

  .info {
    width: calc(100% - 98px);

    .phone {
      overflow-wrap: break-word;
    }

    .info-detail {
      @include flex(flex-start);

      > div {
        @include flex(flex-start);

        &:nth-child(n + 2) {
          margin-left: 32px;
        }
      }
    }

    .description {
      @include flex(flex-start, flex-start);
      margin: 8px 0 16px;
      padding: 8px;

      color: $themeColor;
      background: linear-gradient(
        270deg,
        rgba(5, 85, 206, 0) 0%,
        rgba(5, 85, 206, 0.05) 100%
      );

      > :first-child {
        margin: 6.5px 8px 0 0;
      }

      > :not(:last-child) {
        flex-shrink: 0;
      }

      &-content {
        width: calc(100% - 86px);
        overflow-wrap: break-word;
      }
    }

    .flow-label {
      flex-shrink: 0;
      margin-top: 9.5px;
    }

    .flow-list {
      @include flex(flex-start);
      flex-wrap: wrap;

      > :nth-child(n) {
        margin-bottom: 12px;
      }

      .flow-line {
        width: 70px;
        height: 7px;
        background: #f2f4f7;
      }

      .line-active {
        background: #e6eefa;
      }

      .node {
        @include flex;
        box-sizing: border-box;
        padding: 8px 32px;
        background: #f2f4f7;
        border-radius: 1000px;
        font-size: 16px;
        color: $txtColor-light;
      }

      .node-cancel {
        background: #feebeb;
        color: $color-danger;
      }

      .node-done {
        background: #e6eefa;
        color: $themeColor;
      }

      .node-active {
        background: #ffffff;
        color: $themeColor;
        border: 1px solid $themeColor;
      }

      .node-icon {
        width: 20px;
        height: 20px;
      }
    }
  }
}

.more-info {
  margin-top: 16px;
  padding: 24px 24px 73px;
  border-radius: 4px;
  background: #ffffff;

  > :nth-child(n + 2) {
    margin-top: 24px;
  }
}

.timeline {
  margin-left: 110px;

  .time-box {
    @include flex-col;
    position: absolute;
    left: -110px;
    padding: 8px 16px;
    border-radius: 4px;
    background: #ffffff;
    border: 1px solid #e0e0e0;

    .day {
      font-size: 24px;
      font-weight: bold;
    }

    .time {
      color: $txtColor-light;
    }
  }

  :deep(.el-timeline-item__tail) {
    border-color: #e8e8e8;
  }

  .dot {
    position: absolute;
    top: 0;
    left: -3px;
    width: 16px;
    height: 16px;
  }

  .approval-top {
    @include flex;

    .approval-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }

  .approval-content {
    padding: 16px;
    border-radius: 4px;
    background: #f2f4f7;

    > div {
      @include flex(flex-start);

      &:nth-child(n + 2) {
        margin-top: 8px;
      }

      > :first-child {
        flex-shrink: 0;
        width: 70px;
      }
    }

    .desc {
      @include flex(flex-start, flex-start);

      &-value {
        width: calc(100% - 70px);
        overflow-wrap: break-word;
      }
    }
  }
}

:deep(.audit-form) > .el-form-item {
  width: 100%;

  .el-form-item__content {
    width: calc(100% - 75px);
  }
}

.btn-box {
  position: sticky;
  bottom: 0;
  padding: 16px 24px;
  width: 100%;
  background: #ffffff;
  border-radius: 0px 0px 4px 4px;
  border-width: 1px 0px 0px 0px;
  border-style: solid;
  border-color: #e8e8e8;
  text-align: right;
}

.label {
  color: $txtColor-light;
}

.type-value {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.blue {
  color: $themeColor;
}

.circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: $themeColor;
}

.title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
}

.el-button {
  padding: 11px 39px;
  font-size: 16px;
  font-weight: normal;
}
</style>
