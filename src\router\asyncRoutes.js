/* 
此模板用于后端返回权限菜单的情况，如果菜单鉴权在前端则component可以直接为组件

路由配置规则：

一级路由
  path: 路径
  name: 不需要
  hidden: Boolean，不显示在菜单栏
  component: 统一为layout或router-view（前者为框架，后者仅作为嵌套时的过渡）
  redirect: 自动跳转路由
  alwaysShow: 为true时，在只有一个显示子菜单的情况下，该子菜单不会提示至父级
  meta.title: 菜单栏和标签页的标题，${}语法可以获取route.params里的属性作为标题变量
  meta.icon: 菜单栏的图标（kebab-case命名，可以是element的icon，也可以是svg-icon）
  meta.affix: 固定存在于标签栏，不可删除
  meta.tagIcon: 此值存在时，该菜单标签显示为该值对应的图标

二级路由
  path: 相对一级路由的路径
  name: 必须有（PascalCase命名）
  hidden: Boolean，不显示在菜单栏
  component: 统一为'views/'下扁平管理（kebab-case命名）
  meta.title: 菜单栏和标签页的标题，${}语法可以获取route.params里的属性作为标题变量
  meta.icon: 不需要
  meta.target: 当为跳转外部链接时，不需要name，target取值可以是blank、self；
               当为内嵌iframe打开时，需要name，target取值inner
  meta.noCache: 不缓存
  meta.cacheAs: 缓存组件名（用于临时tab页面缓存）

*/

export default [
  {
    path: "/number-manage",
    component: "layout",
    redirect: "/",
    alwaysShow: true,
    meta: { title: "号码管理", icon: "function" },
    children: [
      {
        path: "number-list",
        name: "NumberList",
        meta: { title: "号码管理列表" },
        component: "views/number-manage/index",
      },
      {
        path: "number-config/:id",
        name: "NumberConfig",
        hidden: true,
        meta: { title: "号码详情" },
        component: "views/number-manage/config",
      },
    ],
  },
  {
    path: "/unified-number-manage",
    component: "layout",
    redirect: "/",
    alwaysShow: true,
    meta: { title: "统签号码管理", icon: "function" },
    children: [
      {
        path: "index",
        name: "UnifiedNumberManage",
        meta: { title: "统签号码列表" },
        component: "views/unified-number-manage",
      },
    ],
  },
  {
    path: "/template",
    component: "layout",
    redirect: "/",
    alwaysShow: true,
    meta: { title: "模板", icon: "function" },
    children: [
      {
        path: "list-page",
        name: "ListPage",
        meta: { title: "列表" },
        component: "views/template/list",
      },
      {
        path: "tree-page",
        name: "TreePage",
        meta: { title: "树型" },
        component: "views/template/tree-page",
      },
    ],
  },
  {
    path: "/system-manage",
    component: "layout",
    redirect: "/",
    alwaysShow: true,
    meta: { title: "系统管理", icon: "function" },
    children: [
      {
        path: "interceptor-config",
        name: "InterceptorConfig",
        meta: { title: "协同号码拦截库配置" },
        component: "views/collaborative-number-block",
      },
    ],
  },
  {
    path: "/organize-manage",
    component: "layout",
    redirect: "/",
    alwaysShow: true,
    meta: { title: "组织管理", icon: "function" },
    children: [
      {
        path: "framework",
        name: "Framework",
        meta: { title: "组织架构管理" },
        component: "views/organize/framework",
      },
      {
        path: "role",
        name: "Role",
        meta: { title: "角色管理" },
        component: "views/organize/role",
      },
      {
        path: "user",
        name: "User",
        meta: { title: "用户管理" },
        component: "views/organize/user",
      },
    ],
  },
  {
    path: "/my-apply",
    component: "layout",
    redirect: "/",
    alwaysShow: true,
    meta: { title: "我的申请", icon: "function" },
    children: [
      {
        path: "apply-list",
        name: "ApplyList",
        meta: { title: "我的申请列表" },
        component: "views/my-apply/list/index",
      },
      {
        path: "apply-detail",
        name: "ApplyDetail",
        hidden: true,
        meta: { title: "申请详情" },
        component: "views/my-apply/detail/index",
      },
    ],
  },
  {
    path: "/audit-manage",
    component: "layout",
    redirect: "/",
    alwaysShow: true,
    meta: { title: "审核管理", icon: "function" },
    children: [
      {
        path: "audit-list",
        name: "AuditList",
        meta: { title: "审核管理列表" },
        component: "views/audit-manage/index",
      },
    ],
  },
];
