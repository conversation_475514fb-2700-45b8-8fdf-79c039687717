<template>
  <div class="h-full flex" v-loading="loading">
    <RoleAside ref="roleAside" @edit-role="addRole" @set-current="setCurrent" />
    <div class="flex-1 rounded-r bg-white border-0 border-l-[#E8E8E8] border-l-[1px] border-solid">
      <div class="h-[56px] flex items-center justify-between px-6 border-transparent border-b-[1px] border-solid border-b-[#E0E0E0]">
        <div class="color-[#262626] font-bold text-[16px]">{{ role.ROLE_NAME }}列表（{{ total }}人）</div>
        <el-button
          type="primary"
          size="small"
          @click="openMember">
          <svg-icon icon="add"></svg-icon>
          添加成员
        </el-button>
      </div>
      <div class="h-[calc(100%-56px)] flex flex-col px-6 py-4">
        <div class="info mb-4" v-show="role.ROLE_DESC">
          <img src="~@/assets/images/organize/user.png" alt="" class="mr-1">
          角色描述：{{ role.ROLE_DESC }}
        </div>
        <el-form :model="form" ref="form" label-width="auto" :inline="true" size="small">
          <el-form-item
            label="关键字"
            prop="searchCode">
            <el-input
              v-model="form.searchCode"
              clearable
              style="width: 220px"
              placeholder="请输入关键字进行搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="small"
              plain
              @click="reset">
              <svg-icon icon="reset"></svg-icon>
              重置
            </el-button>
            <el-button
              type="primary"
              @click="getRoleUserList(1)">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
        <div class="flex-1 overflow-hidden">
          <el-table
            ref="table"
            :data="list"
            v-loading="loading"
            stripe
            height="100%"
            fit
            style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="60" />
            <el-table-column
              label="成员姓名"
              prop="userName" />
            <el-table-column
              label="所属单位"
              prop="deptName" />
            <el-table-column
              label="联系电话"
              prop="mobile" />
            <el-table-column
              label="添加时间"
              prop="createTime" />
            <el-table-column
              label="添加人"
              prop="creator" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-link
                  type="danger"
                  :underline="false"
                  @click="delUser(scope.row)"
                  >删除</el-link
                >
              </template>
            </el-table-column>
            <template #empty>
              <el-empty description="暂无数据"></el-empty>
            </template>
          </el-table>
        </div>
        <div
          class="y-footer"
          style="padding-right: 0">
          <pagination
            :current-page.sync="form.pageNo"
            :page-size.sync="form.pageSize"
            :total="total"
            @page="handleChange"></pagination>
        </div>
      </div>
    </div>
    <AddRole :visible.sync="addRoleVisible" :data="current" @success="fetchData"></AddRole>
    <MemberDrawer :visible.sync="addMemberVisible" :data="role" @success="getRoleUserList(1)"></MemberDrawer>
  </div>
</template>

<script>
import RoleAside from '../components/RoleAside.vue'
import AddRole from '../components/AddRole.vue'
import MemberDrawer from '../components/MemberDrawer.vue'
import { getRoleUserList, roleUserDel } from '@/api/org'

export default {
  name: 'OrganizeRole',
  components: {
    RoleAside,
    AddRole,
    MemberDrawer
  },
  data() {
    return {
      loading: false,
      form: {
        pageType: '3',
        pageNo: 1,
        pageSize: 15,
        searchCode: ''
      },
      list: [],
      addRoleVisible: false,
      addMemberVisible: false,
      role: {},
      current: {},
      total: 0
    }
  },
  methods: {
    addRole(data) {
      this.addRoleVisible = true
      this.current = Object.assign({}, data)
    },
    setCurrent(data) {
      this.role = Object.assign({}, data)
      this.current = Object.assign({}, data)
      this.getRoleUserList()
    },
    fetchData() {
      this.$refs.roleAside.fetchData()
    },
    async getRoleUserList(page) {
      this.loading = true
      this.form.pageNo = page || this.form.pageNo
      const [err, res] = await getRoleUserList({...this.form, roleId: this.role.ROLE_ID})
      this.list = res?.data || []
      this.total = res?.totalRow || 0
      this.loading = false
    },
    reset() {
      this.form = {
        pageType: '3',
        pageNo: 1,
        pageSize: 15,
        searchCode: ''
      }
    },
    handleChange(page, size) {
      this.form.pageNo = page
      this.form.pageSize = size
      this.getRoleUserList()
    },
    async delUser(data) {
      await this.$confirm('是否删除该成员?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      this.loading = true
      const [err, res] = await roleUserDel({ roleId: this.role.ROLE_ID, userIds: data.userId })
      if (res) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.getRoleUserList(1)
          },
        })
      }
      this.loading = false
    },
    openMember() {
      this.addMemberVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  align-items: center;
  height: 46px;
  border-radius: 4px;
  padding: 0 16px;
  color: #0555CE;
  font-size: 14px;
  background: linear-gradient(270deg, rgba(5, 85, 206, 0) 0%, rgba(5, 85, 206, 0.1) 100%);  
}
</style>