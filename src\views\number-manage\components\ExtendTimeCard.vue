<template>
  <base-card class="extend-time-card card-container y-container">
    <div class="info">
      <span>延迟时间</span>
      <p class="font-bold">
        <span class="text-2xl">{{ data.extendMinutes || 0 }}</span>
        <span class="text-xl">分钟</span>
      </p>
      <span class="label">添加时间：{{ data.createTime || '-' }}</span>
    </div>
  </base-card>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/modules/info-card.scss';

.card-container {
  flex: 0 0 273px;
  padding: 16px 24px;
  background: linear-gradient(270deg, rgba(5, 85, 206, 0.05) 0%, rgba(5, 85, 206, 0.1) 100%),
    url('@/assets/images/number/worktime-icon.svg') no-repeat top right / 100px 97px;

  .label {
    color: $txtColor-light;
  }
}
</style>
