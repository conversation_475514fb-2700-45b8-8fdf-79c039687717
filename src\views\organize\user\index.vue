<template>
  <div class="h-full flex" id="user" v-loading="loading">
    <UserAside @set-current="setCurrent" />
    <div class="flex-1 rounded-r flex flex-col">
      <div class="h-[56px] bg-white flex items-center justify-between px-6 border-transparent border-b-[1px] border-solid border-b-[#E0E0E0]">
        <div class="color-[#262626] font-bold text-[16px]">用户管理列表</div>
        <el-button
          type="primary"
          size="small"
          @click="handleAdd">
          <svg-icon icon="add"></svg-icon>
          新增用户
        </el-button>
      </div>
      <div class="bg-white rounded-b px-6 py-4 mb-4">
        <el-form :model="form" ref="form" label-width="auto" :inline="true" size="small" class="search-form">
          <el-form-item label="状态" label-width="60px">
            <el-select v-model="form.state" placeholder="全部" clearable>
              <el-option :label="label" :value="value" v-for="(label, value) in state" :key="value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="姓名/登录账号/手机号"
            prop="user">
            <el-input
              v-model="form.user"
              clearable
              style="width: 220px"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="small"
              plain
              @click="handleReset">
              <svg-icon icon="reset"></svg-icon>
              重置
            </el-button>
            <el-button
              type="primary"
              @click="handleSeach">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="flex-1 overflow-hidden">
        <empty-wrapper
          :toggle="list.length == 0">
          <div class="h-full overflow-auto grid grid-cols-2 items-start gap-4 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2">
            <div class="box" v-for="(item, index) in list" :key="index">
              <div class="status flex items-center absolute top-4 right-4">
                <span class="color-[#868686]">状态：</span>
                <div class="w-2 h-2 rounded-full bg-[#0555CE] mx-1" v-if="item.USER_STATE == '0'"></div>
                <div class="w-2 h-2 rounded-full bg-[#FA9904] mx-1" v-if="item.USER_STATE == '1'"></div>
                <span class="color-[#0555CE]">{{ state[item.USER_STATE] }}</span>
              </div>
              <div class="avatar relative mr-4" v-if="item.SEX == '0'">
                <img src="~@/assets/images/organize/woman.png" alt="">
                <img src="~@/assets/images/organize/sex-1.png" alt="" class="w-[24px] h-[24px] absolute bottom-0 right-0">
              </div>
              <div class="avatar relative mr-4" v-else>
                <img src="~@/assets/images/organize/man.png" alt="">
                <img src="~@/assets/images/organize/sex-2.png" alt="" class="w-[24px] h-[24px] absolute bottom-0 right-0">
              </div>
              <div class="info flex-1 overflow-hidden z-10">
                <div class="flex items-center mb-2">
                  <div class="color-[#262626] font-bold text-[24px] leading-9">{{ item.AGENT_NAME || '--' }}</div>
                  <div style="background: rgba(92, 124, 250, 0.1);color: #5C7CFA;" class="inline-block h-[22px] leading-[22px] px-2 ml-2 rounded" v-for="c in item.ROLE_LIST" :key="c" v-show="c">{{ c }}</div>
                </div>
                <div class="flex items-center mb-1 flex-wrap">
                  <div class="label">登录账号：</div>
                  <div class="value mr-6">{{ item.USER_ACCT }}</div>
                  <div class="label">手机号：</div>
                  <div class="value mr-6">{{ item.MOBILE }}</div>
                  <div class="label">邮箱：</div>
                  <div class="value mr-6">{{ item.EMAIL }}</div>
                </div>
                <div class="flex items-center mb-1">
                  <div class="label">所属单位：</div>
                  <div class="value mr-6">{{ item.DEPT_NAME }}</div>
                </div>
                <div class="flex items-center mb-1">
                  <div class="label">最近登录IP：</div>
                  <div class="value mr-6">{{ item.LAST_LOGIN_IP }}</div>
                  <div class="label">最近登录时间：</div>
                  <div class="value mr-6">{{ item.LOGIN_TIME }}</div>
                </div>
                <div class="flex items-center justify-end mt-4 relative z-[100]">
                  <el-button
                    type="danger"
                    size="small"
                    plain
                    @click="deleteUser(item)">
                    删除
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    plain
                    @click="updateUserState(item)">
                    {{ item.USER_STATE == '0' ? '锁定' : '解锁' }}
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    plain
                    @click="resetPassword(item)">
                    重置密码
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    plain
                    @click="hendleEdit(item)">
                    编辑
                  </el-button>
                </div>
              </div>
              <!-- <img src="~@/assets/images/organize/user-bg.png" alt="" class="w-[260px] h-[76px] absolute bottom-0 left-0"> -->
            </div>
          </div>
          <div
            class="y-footer bg-white rounded border-0"
            style="padding-right: 0">
            <pagination
              :current-page.sync="form.pageNo"
              :page-size.sync="form.pageSize"
              :total="total"
              @page="handleChange"></pagination>
          </div>
        </empty-wrapper>
      </div>
    </div>

    <AddUser :visible.sync="visible" :dept-id="deptId" :data="userData" @success="getUserList(1)" />
  </div>
</template>

<script>
import UserAside from '../components/UserAside.vue'
import AddUser from '../components/AddUser.vue'
import { getUserList, updateUserState, deleteUser, resetPassword, userInfo } from '@/api/org'

export default {
  name: 'User',
  components: {
    UserAside,
    AddUser
  },
  data() {
    return {
      loading: false,
      form: {
        pageType: '3',
        pageNo: 1,
        pageSize: 15,
        state: '',
        deptCode: '',
        deptId: '',
        user: ''
      },
      list: [],
      total: 0,
      state: {
        '0': '正常',
        '1': '锁定'
      },
      visible: false,
      deptId: '',
      userData: {},
      colors: {
        '1': '#FCC419',
        '2': '#5C7CFA',
        '3': '#22B8CF',
        '4': '#51CF66'
      }
    }
  },
  methods: {
    async hendleEdit(data) {
      this.loading = true
      const [err, res] = await userInfo({ userId: data.USER_ID })
      if (res) {
        this.visible = true
        this.$nextTick(() => {
          this.userData = Object.assign({}, res.data)
          if (this.userData.ROLE_IDS && typeof this.userData.ROLE_IDS == 'string') {
            this.userData.ROLE_IDS = this.userData.ROLE_IDS.split(',')
          }
        })
      }
      this.loading = false
    },
    async resetPassword(data) {
      await this.$confirm('是否重置该用户密码？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      this.loading = true
      const [err, res] = await resetPassword({ userId: data.USER_ID })
      if (res) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 800,
          onClose: () => {
            this.getUserList(1)
          },
        })
      }
      this.loading = false
    },
    async deleteUser(data) {
      await this.$confirm('是否删除该用户', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      this.loading = true
      const [err, res] = await deleteUser({ userId: data.USER_ID })
      if (res) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 800,
          onClose: () => {
            this.getUserList(1)
          },
        })
      }
      this.loading = false
    },
    async updateUserState(data) {
      console.log(data)
      await this.$confirm(`是否${data.USER_STATE == '0' ? '锁定' : '解锁'}该用户？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      this.loading = true
      const [err, res] = await updateUserState({ userId: data.USER_ID, state: data.USER_STATE == '0' ? '1' : '0' })
      if (res) {
        this.$message({
          message: res.msg,
          type: 'success',
          duration: 800,
          onClose: () => {
            this.getUserList(1)
          },
        })
      }
      this.loading = false
    },
    handleAdd() {
      this.visible = true
    },
    async getUserList(page) {
      if (page) this.form.pageNo = page
      this.loading = true
      const [err, res] = await getUserList(this.form)
      if (res) {
        res.data.forEach(item => {
          if (item.ROLE_LIST && typeof item.ROLE_LIST == 'string') {
            item.ROLE_LIST = item.ROLE_LIST.split(',')
          }
        })
        this.list = res.data
        this.total = res.totalRow
      }
      this.loading = false
    },
    handleReset() {
      this.form = {
        pageType: '3',
        pageNo: 1,
        pageSize: 15,
        state: '',
        deptCode: '',
        deptId: '',
        user: ''
      }
      this.$refs.form.resetFields()
    },
    handleSeach() {
      this.getUserList(1)
    },
    handleChange(page, size) {
      this.form.pageNo = page
      this.form.pageSize = size
      this.getUserList()
    },
    setCurrent(data) {
      console.log(data)
      this.form.deptCode = data.deptCode
      this.deptId = data.deptId
      this.getUserList(1)
    }
  },
  created() {
    this.getUserList()
  }
}
</script>

<style lang="scss" scoped>
#user {
  > .aside-bar {
    @include card;
    border-right: 1px solid #E8E8E8;
    margin-right: 16px;
  }
  .search-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .box {
    position: relative;
    min-height: 214px;
    border-radius: 4px;
    // background: #fff;
    padding: 24px;
    display: flex;
    align-items: flex-start;
    color: #262626;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;
    background: url('@/assets/images/organize/user-bg.png') center / 100% 100% no-repeat;
    .label {
      color: #868686;
    }
    .avatar {
      width: 88px;
      height: 88px;
      border-radius: 50%;
    }
  }
}
</style>