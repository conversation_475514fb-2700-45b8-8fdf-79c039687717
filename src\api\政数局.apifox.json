{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "政数局", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 59025950, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 5615216, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "号码管理", "id": 60041064, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5615216, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "统签号码管理", "id": 61868144, "auth": {}, "securityScheme": {}, "parentId": 60041064, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5615216, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "统签号码新增", "api": {"id": "323397695", "method": "POST", "path": "/yc-govphonemgmt/servlet/phoneNumber", "parameters": {"path": [], "query": [{"id": "P5CcQpdljw", "name": "action", "required": true, "description": "", "example": "unifiedPhoneNumberAdd", "type": "string"}], "cookie": [], "header": [{"id": "ZmVqSYtfQt", "name": "<PERSON><PERSON>", "required": true, "description": "", "example": "JSESSIONID=2E9894311EFE3254CAC09B9BE3AF5492; JSESSIONID=E53BA866AC285659CAC07B45CB99ADD9; JSESSIONIDSSO=C5614822288DE8D40AF27F37248E026D; Hm_lvt_b9e2d180a3f5f12f1c0498f881395280=**********; HMACCOUNT=5A4A1E306C5A23B5; Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280=**********", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}, {"id": "*********", "code": 404, "name": "失败", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\"msg\":\"操作成功!\",\"state\":1}", "responseId": *********, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "成功示例", "data": "", "responseId": *********, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "x-apifox-orders": []}, "examples": [{"value": "{\r\n    \"data\":{\r\n        \"phoneNumbers\":\"***********\"\r\n    }\r\n\r\n}", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 0, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5615216, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "统签号码删除", "api": {"id": "323397696", "method": "POST", "path": "/yc-govphonemgmt/servlet/phoneNumber", "parameters": {"path": [], "query": [{"id": "kTeZu9MW8U", "name": "action", "required": true, "description": "", "example": "unifiedPhoneNumberDelete", "type": "string"}], "cookie": [], "header": [{"id": "seyTd4XdY4", "name": "<PERSON><PERSON>", "required": true, "description": "", "example": "JSESSIONID=2E9894311EFE3254CAC09B9BE3AF5492; JSESSIONID=E53BA866AC285659CAC07B45CB99ADD9; JSESSIONIDSSO=C5614822288DE8D40AF27F37248E026D; Hm_lvt_b9e2d180a3f5f12f1c0498f881395280=**********; HMACCOUNT=5A4A1E306C5A23B5; Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280=**********", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}, {"id": "*********", "code": 404, "name": "失败", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\"msg\":\"操作成功!\",\"state\":1}", "responseId": *********, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "成功示例", "data": "", "responseId": *********, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "x-apifox-orders": []}, "examples": [{"value": "{\r\n    \"data\":{\r\n        \"phoneNumberIds\":\"82472618796608447625687\"\r\n    }\r\n\r\n}", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 6, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5615216, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "统签号码导出", "api": {"id": "323397697", "method": "POST", "path": "/yc-govphonemgmt/servlet/phoneNumber", "parameters": {"path": [], "query": [{"id": "C39bM5vZlW", "name": "action", "required": true, "description": "", "example": "unifiedPhoneNumberExport", "type": "string"}], "cookie": [], "header": [{"id": "vZxVA24PvB", "name": "<PERSON><PERSON>", "required": true, "description": "", "example": "JSESSIONID=2E9894311EFE3254CAC09B9BE3AF5492; JSESSIONID=E53BA866AC285659CAC07B45CB99ADD9; JSESSIONIDSSO=C5614822288DE8D40AF27F37248E026D; Hm_lvt_b9e2d180a3f5f12f1c0498f881395280=**********; HMACCOUNT=5A4A1E306C5A23B5; Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280=**********", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}, {"id": "*********", "code": 404, "name": "失败", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{\"msg\":\"操作成功!\",\"data\":{\"downloadTaskId\":\"82472612687598446113800\",\"count\":1},\"state\":1}", "responseId": *********, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "成功示例", "data": "", "responseId": *********, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [], "jsonSchema": {"type": "object", "x-apifox-orders": []}, "examples": [{"value": "{\r\n    \"data\":{\r\n        \"phoneNumbers\":\"***********\"\r\n    }\r\n\r\n}", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 12, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5615216, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}, {"name": "统签号码列表接口", "api": {"id": "323397698", "method": "POST", "path": "/yc-govphonemgmt/webcall", "parameters": {"path": [], "query": [{"id": "5tO5rTY2HI", "name": "action", "required": true, "description": "", "example": "phoneNumber.unifiedList", "type": "string"}], "cookie": [], "header": [{"id": "z9oaB4QTx2", "name": "<PERSON><PERSON>", "required": true, "description": "", "example": "JSESSIONID=2E9894311EFE3254CAC09B9BE3AF5492; JSESSIONID=E53BA866AC285659CAC07B45CB99ADD9; JSESSIONIDSSO=C5614822288DE8D40AF27F37248E026D; Hm_lvt_b9e2d180a3f5f12f1c0498f881395280=**********; HMACCOUNT=5A4A1E306C5A23B5; Hm_lpvt_b9e2d180a3f5f12f1c0498f881395280=**********", "type": "string"}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "*********", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}, {"id": "*********", "code": 404, "name": "失败", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "", "oasExtensions": ""}], "responseExamples": [{"name": "成功示例", "data": "{}", "responseId": *********, "ordering": 1, "description": "", "oasKey": "", "oasExtensions": ""}, {"name": "成功示例", "data": "", "responseId": *********, "ordering": 2, "description": "", "oasKey": "", "oasExtensions": ""}], "requestBody": {"type": "application/json", "parameters": [{"id": "Nl6rkif23U", "name": "data", "required": true, "description": "返回数据", "example": "{\"pageIndex\":1,\"pageSize\":15,\"pageType\":3,\"keyword\":\"测试\",\"beginDate\":\"\",\"endDate\":\"\"}", "type": "string", "contentType": ""}], "jsonSchema": {"type": "object", "x-apifox-orders": []}, "examples": [{"value": "{\r\n    \"data\":{\r\n        \"number\":\"***********\"\r\n    }\r\n}", "mediaType": "application/json", "description": ""}], "oasExtensions": ""}, "description": "{\n\"ID\": \"主键ID\",\n\"CHAT_ID\": \"聊天唯一标识ID\",\n\"OBJ_ID\": \"对象唯一标识ID\",\n\"DATE_ID\": \"日期标识\",\n\"MSG_TIME\": \"消息时间\",\n\"MSG_CONTENT\": \"消息内容\",\n\"CUSTOMER_TYPE\": \"客户画像标签-客户类型\",\n\"INTENTION\": \"客户需求意向标签\",\n\"INTERACTION_DEPTH\": \"客户互动深度标签\",\n\"BUSINESS_FOCUS\": \"客户业务关注点标签\",\n\"PAIN_POINT\": \"客户业务痛点标签\",\n\"DECISION_STYLE\": \"客户决策风格标签\",\n\"FOLLOW_UP_FEASIBILITY\": \"客户跟进可行性标签\",\n\"ROBOT_ASSISTANT\": \"是否由机器人助理服务\",\n\"COMPLAINT_RISK\": \"客户投诉风险标签\",\n\"HANGUP_BEHAVIOR\": \"客户挂断行为标签\",\n\"CREATED_AT\": \"记录创建时间\"\n}", "tags": [], "status": "developing", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 18, "cases": [], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5615216, "oasExtensions": "", "type": "http", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 6859869, "updatedAt": "2025-06-13T08:49:57.000Z", "name": "根目录", "type": "root", "children": [], "moduleId": 5615216, "parentId": 0, "id": 6859869, "ordering": [], "items": []}], "schemaCollection": [], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.6969016"], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [{"id": "6575702", "variables": []}], "moduleVariables": [{"id": "5615216", "variables": []}], "commonParameters": {"id": 794895, "createdAt": "2025-06-20T09:05:49.000Z", "updatedAt": "2025-07-17T08:05:33.000Z", "deletedAt": null, "parameters": {"query": [{"type": "string", "defaultEnable": true, "required": true, "description": "", "defaultValue": "", "name": ""}], "body": [{"type": "string", "defaultEnable": true, "required": true, "description": "", "defaultValue": "", "name": ""}], "cookie": [{"name": "JSESSIONIDSSO", "defaultEnable": true, "type": "string", "id": "1DRHNcJ8VJ", "defaultValue": "DD7721311656AA71F9AC7205802481A9", "schema": {"type": "string", "default": "DD7721311656AA71F9AC7205802481A9"}}, {"name": "JSESSIONIDSSONODE", "defaultEnable": true, "type": "string", "id": "mmk2CyaxRe", "defaultValue": "DD7721311656AA71F9AC7205802481A9", "schema": {"type": "string", "default": "DD7721311656AA71F9AC7205802481A9"}}], "header": [{"type": "string", "defaultEnable": true, "required": true, "description": "", "defaultValue": "", "name": ""}]}, "projectId": 6575702, "creatorId": 2829425, "editorId": 672962}, "projectSetting": {"id": "6608205", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": true, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}, "folderShareExpandModeSettings": {"expandId": [], "mode": "AUTO"}}, "initialDisabledMockIds": [], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectAssociations": []}