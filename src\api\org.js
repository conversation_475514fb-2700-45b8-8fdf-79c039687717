import { get, post } from '@/http/request'

// 组织管理API

/**
 * 获取部门树列表
 * @description 获取完整的部门树形结构，自动包含根节点
 * @returns {Promise} 返回部门树列表请求的Promise对象
 */
export function getDeptTree() {
  return post('/yc-govphonemgmt/webcall?action=dept.deptTree', { data: {} })
} 

/**
 * 新增部门
 * @param {Object} data - 新增部门参数
 * @param {string} data.deptName - 部门名称
 * @param {string} data.address - 地址
 * @param {string} data.parentId - 父部门ID
 * @param {string} data.responsibleName - 负责人
 * @param {string} data.responsibleNumber - 负责人联系方式
 * @returns {Promise} 返回新增部门请求的Promise对象
 */
export function addDept(data) {
  return post('/yc-govphonemgmt/servlet/dept?action=deptAdd', { data })
}

/**
 * 删除部门
 * @param {Object} data - 删除部门参数
 * @param {string} data.deptId - 部门ID
 * @returns {Promise} 返回删除部门请求的Promise对象
 */
export function delDept(data) {
  return post('/yc-govphonemgmt/servlet/dept?action=del', { data })
}

/**
 * 获取部门信息
 * @param {Object} data - 获取部门信息参数
 * @param {string} data.deptId - 部门ID
 * @returns {Promise} 返回获取部门信息请求的Promise对象
 */
export function getDeptInfo(data) {
  return post('/yc-govphonemgmt/webcall?action=dept.deptInfo', { data })
}

/**
 * 编辑部门
 * @param {Object} data - 编辑部门信息
 * @returns {Promise} 返回编辑部门信息的Promise对象
 */
export function deptEdit(data) {
  return post('/yc-govphonemgmt/servlet/dept?action=deptEdit', { data })
}

/**
 * 部门用户列表
 * @param {Object} data - 获取部门用户列表
 * @param {String} data.pageType -分页类型
 * @param {Number} data.pageNo -页码
 * @param {Number} data.pageSize - 页数
 * @param {String} data.deptId - 部门ID
 * @returns {Promise} 返回获取部门用户列表的Promise对象
 */
export function getDeptUserList(data) {
  return post('/yc-govphonemgmt/webcall?action=dept.deptUserList', { data })
}

/**
 * 获取角色列表
 * @param {Object} data - 获取角色列表
 * @param {String} data.pageType -分页类型
 * @param {Number} data.pageNo -页码
 * @param {Number} data.pageSize - 页数
 * @returns {Promise} 返回获取角色列表的Promise对象
 */
export function getRoleList(data) {
  return post('/yc-govphonemgmt/webcall?action=role.roleList', { data })
} 

/**
 * 获取权限配置
 * @param {Object} data - 获取权限配置
 * @returns {Promise} 返回获取权限配置的Promise对象
 */
export function getRoleResTree(data) {
  return post('/yc-govphonemgmt/webcall?action=role.roleResTree', { data })
} 

/**
 * 新增自定义角色
 * @param {Object} data - 新增自定义角色数据
 * @param {String} data.roleName - 角色名称
 * @param {String} data.roleDesc - 角色描述
 * @param {Array} data.resIds - 资源ID列表
 * @returns {Promise} 返回新增自定义角色的Promise对象
 */
export function roleAdd(data) {
  return post('/yc-govphonemgmt/servlet/role?action=roleAdd', { data })
}

/**
 * 编辑自定义角色
 * @param {Object} data - 编辑自定义角色数据
 * @param {String} data.roleName - 角色名称
 * @param {String} data.roleDesc - 角色描述
 * @param {Array} data.resIds - 资源ID列表
 * @returns {Promise} 返回编辑自定义角色的Promise对象
 */
export function roleEdit(data) {
  return post('/yc-govphonemgmt/servlet/role?action=roleEdit', { data })
}

/**
 * 获取角色用户列表
 * @param {Object} data - 获取角色用户列表
 * @param {String} data.pageType -分页类型
 * @param {Number} data.pageNo -页码
 * @param {Number} data.pageSize - 页数
 * @param {String} data.roleId - 角色id
 * @returns {Promise} 返回获取角色用户列表的Promise对象
 */
export function getRoleUserList(data) {
  return post('/yc-govphonemgmt/webcall?action=role.roleUserList', { data })
}

/**
 * 删除角色用户
 * @param {Object} data - 删除角色用户
 * @param {String} data.userIds - 用户ID列表
 * @param {String} data.roleId - 角色ID
 * @returns {Promise} 返回删除角色用户的Promise对象
 */
export function roleUserDel(data) {
  return post('/yc-govphonemgmt/servlet/role?action=roleUserDel', { data })
}

/**
 * 删除角色
 * @param {Object} data - 删除角色
 * @param {String} data.roleId - 角色ID
 * @returns {Promise} 返回删除角色的Promise对象
 */
export function roleDel(data) {
  return post('/yc-govphonemgmt/servlet/role?action=roleDel', { data })
}

/**
 * 获取角色选中用户
 * @param {Object} data - 获取权限配置
 * @param {String} data.roleId - 角色ID
 * @returns {Promise} 返回获取角色选中用户的Promise对象
 */
export function getRoleSelectedUser(data) {
  return post('/yc-govphonemgmt/webcall?action=role.roleSelectedUser', { data })
}

/**
 * 编辑角色用户
 * @param {Object} data - 编辑角色用户
 * @param {String} data.roleId - 角色ID
 * @param {String} data.userIds - 用户ID列表
 * @returns {Promise} 返回编辑角色用户的Promise对象
 */
export function editUser(data) {
  return post('/yc-govphonemgmt/servlet/role?action=editUser', { data })
}

/**
 * 获取用户列表
 * @param {Object} data - 获取角色用户列表
 * @param {String} data.pageType -分页类型
 * @param {Number} data.pageNo -页码
 * @param {Number} data.pageSize - 页数
 * @param {String} data.deptId - 部门id
 * @returns {Promise} 返回获取用户列表的Promise对象
 */
export function getUserList(data) {
  return post('/yc-govphonemgmt/webcall?action=user.userList', { data })
}

/**
 * 新增/编辑用户
 * @param {Object} data - 新增/编辑用户
 * @returns {Promise} 返回新增/编辑用户的Promise对象
 */
export function userSave(data) {
  return post('/yc-govphonemgmt/servlet/user?action=UserSave', { data })
}

/**
 * 锁定/解除锁定
 * @param {Object} data - 锁定/解除锁定
 * @param {String} data.userId - 用户id
  * @param {String} data.state - 部用户状态
 * @returns {Promise} 返回锁定/解除锁定的Promise对象
 */
export function updateUserState(data) {
  return post('/yc-govphonemgmt/servlet/user?action=UpdateState', { data })
}

/**
 * 删除用户
 * @param {Object} data - 删除用户
 * @param {String} data.userId - 用户id
 * @returns {Promise} 返回删除用户的Promise对象
 */
export function deleteUser(data) {
  return post('/yc-govphonemgmt/servlet/user?action=delete', { data })
}

/**
 * 重置密码
 * @param {Object} data - 重置密码
 * @param {String} data.userId - 用户id
 * @returns {Promise} 返回重置密码的Promise对象
 */
export function resetPassword(data) {
  return post('/yc-govphonemgmt/servlet/user?action=resetPassword', { data })
}

/**
 * 用户详情
 * @param {Object} data - 用户详情
 * @param {String} data.userId - 用户id
 * @returns {Promise} 返回用户详情的Promise对象
 */
export function userInfo(data) {
  return post('/yc-govphonemgmt/webcall?action=user.userInfo', { data })
}