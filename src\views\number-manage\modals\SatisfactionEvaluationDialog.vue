<template>
  <el-dialog
    title="满意度评价配置申请"
    width="420px"
    :visible.sync="visible"
    @close="handleClose">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="160px">
        <!-- 满意度评价开关 -->
        <el-form-item
          label="是否开启满意度评价"
          prop="satisfactionSwitch">
          <el-switch
            v-model="form.satisfactionSwitch"
            :active-value="1"
            :inactive-value="0">
          </el-switch>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button
        type="primary"
        size="small"
        plain
        @click="handleClose"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        @click="handleSubmit">
        提交申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { myApplyUpdateSatisfaction } from "@/api/my-apply.js";
import { updateSatisfaction } from '@/api/number-manage';

export default {
  name: 'SatisfactionEvaluationDialog',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => null,
    },
    selection: {
      type: Array,
      default: () => [],
    },
    fromWhere: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      form: {
        satisfactionSwitch: true,
      },
      rules: {
        satisfactionSwitch: [{ required: true, message: '请选择是否开启满意度评价', trigger: 'change' }],
      },
    }
  },
  computed: {},
  watch: {
    data: {
      handler(data) {
        if (data) {
          this.form = {
            satisfactionSwitch: data.satisfactionSwitch,
          }
        } else {
          this.reset()
        }
      },
      immediate: true,
    },
  },
  methods: {
    reset() {
      this.form = {
        satisfactionSwitch: true,
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true

      // 提交满意度评价配置修改
      let submitData = {}
      if (this.fromWhere === 'satisfaction') {
        submitData = { switchStatus: this.form.satisfactionSwitch, ...this.data}
      }else {
        submitData = {
          phoneId: this.selection.join(),
          status: this.form.satisfactionSwitch,
        }
      }

      const api = this.fromWhere === 'myApplyDetail' ? myApplyUpdateSatisfaction : updateSatisfaction
      const [err, res] = await api(submitData)

      if (res) {
        this.$message({
          message: '满意度评价配置修改成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
