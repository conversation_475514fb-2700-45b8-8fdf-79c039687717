<template>
  <base-card class="satisfaction-card card-container y-container">
    <div class="info">
      <div class="body">
        <p class="y-item">
          <span class="label">满意度评价是否启用：</span>
          <span class="value">{{ data.satisfactionSwitch === 1 ? '是' : '否' }}</span>
        </p>
      </div>
      <div class="y-bar no-padding">
        <el-button
          type="primary"
          plain
          size="small"
          @click="$emit('edit')"
          >修改</el-button
        >
      </div>
    </div>
  </base-card>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  methods: {},
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/modules/info-card.scss';

.card-container {
  padding: 16px;
  background: rgba(5, 85, 206, 0.05) url('@/assets/images/number/satisfaction-bg.svg') no-repeat center right / contain;
}
</style>
