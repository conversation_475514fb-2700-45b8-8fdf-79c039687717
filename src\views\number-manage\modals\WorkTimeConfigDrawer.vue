<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="120px">
        <!-- <div
          class="y-bar no-padding"
          style="padding-bottom: 24px">
          <div
            v-for="key in [1, 2]"
            :key="key"
            class="type-card"
            :class="{ active: type === key }"
            @click="type = key"></div>
        </div> -->

        <!-- 工作时间段配置 -->
        <template v-if="type === 1">
          <el-form-item
            v-for="index in [1, 2, 3]"
            :key="index"
            :label="`工作时间${index}`"
            :prop="`workTime${index}Start`">
            <div class="time-slot-container">
              <el-time-picker
                v-model="form[`workTime${index}Start`]"
                placeholder="开始时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                style="width: 230px">
              </el-time-picker>

              <span>-</span>

              <el-time-picker
                v-model="form[`workTime${index}End`]"
                placeholder="结束时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                style="width: 230px">
              </el-time-picker>

              <!-- <el-button
                v-if="index === 0"
                type="primary"
                plain
                size="small"
                class="add-btn"
                icon="el-icon-plus"
                @click="addTimeSlot">
              </el-button>
              <el-button
                v-else
                type="primary"
                plain
                size="small"
                class="remove-btn"
                icon="el-icon-minus"
                @click="removeTimeSlot(index)">
              </el-button> -->
            </div>
          </el-form-item>
        </template>
        <!-- <template v-else>
          <el-collapse>
            <el-collapse-item>
              <template slot="title"> 一致性 Consistency<i class="header-icon el-icon-info"></i> </template>
              <el-form-item
                v-for="(timeSlot, index) in form.timeSlots"
                :key="index"
                :label="`工作时间${index + 1}`"
                :prop="`timeSlots.${index}.startTime`">
                <div class="time-slot-container">
                  <el-time-picker
                    v-model="timeSlot.startTime"
                    placeholder="开始时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    style="width: 230px">
                  </el-time-picker>

                  <span>-</span>

                  <el-time-picker
                    v-model="timeSlot.endTime"
                    placeholder="结束时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    style="width: 229px">
                  </el-time-picker>

                  <el-button
                    v-if="index === 0"
                    type="primary"
                    plain
                    size="small"
                    class="add-btn"
                    icon="el-icon-plus"
                    @click="addTimeSlot">
                  </el-button>
                  <el-button
                    v-else
                    type="primary"
                    plain
                    size="small"
                    class="remove-btn"
                    icon="el-icon-minus"
                    @click="removeTimeSlot(index)">
                  </el-button>
                </div>
              </el-form-item>
            </el-collapse-item>
          </el-collapse>
        </template> -->

        <!-- 工作日选择 -->
        <el-form-item
          label="工作日"
          prop="workDays">
          <MultiSwitch
            v-model="form.workDays"
            :options="weekDaysOptions"
            multiple>
          </MultiSwitch>
        </el-form-item>

        <el-form-item
          label="是否跳过节假日"
          prop="skipHoliday">
          <el-switch
            v-model="form.skipHoliday"
            :active-value="1"
            :inactive-value="0">
          </el-switch>
        </el-form-item>

        <el-form-item
          label="是否启用"
          prop="isEnabled">
          <el-switch
            v-model="form.isEnabled"
            active-value="1"
            inactive-value="0">
          </el-switch>
        </el-form-item>
      </el-form>

      <div class="y-footer">
        <el-button
          type="primary"
          size="small"
          plain
          @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          size="small"
          :loading="loading"
          @click="handleSubmit">
          提交申请
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { myApplyUpdateWorkTime } from "@/api/my-apply.js";
import { addWorkTime, updateWorkTime } from '@/api/number-manage';
import { weekDays } from '@/utils/constant';

export default {
  name: 'WorkTimeConfigDrawer',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '工作时间配置申请',
    },
    data: {
      type: Object,
      default: () => null,
    },
    selection: {
      type: Array,
      default: () => [],
    },
    fromWhere: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      type: 1,
      weekDays,
      form: {
        isEnabled: '1', // 状态0-待审核，1-已生效，对应启用状态
        workTime1Start: '08:30:00',
        workTime1End: '12:00:00',
        workTime2Start: '13:30:00',
        workTime2End: '18:00:00',
        workTime3Start: '',
        workTime3End: '',
        workDays: ['1', '2', '3', '4', '5'],
        skipHoliday: 1, // 是否跳过节假日，1-是，0-否
      },
      rules: {
        isEnabled: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
        workDays: [{ required: true, message: '请选择工作日', trigger: 'change' }],
        skipHoliday: [{ required: true, message: '请选择是否跳过节假日', trigger: 'change' }],
        workTime1Start: [{ required: true, message: '请选择工作时间1开始时间', trigger: 'change' }],
        workTime1End: [{ required: true, message: '请选择工作时间1结束时间', trigger: 'change' }],
        workTime2Start: [{ required: true, message: '请选择工作时间2开始时间', trigger: 'change' }],
        workTime2End: [{ required: true, message: '请选择工作时间2结束时间', trigger: 'change' }],
        workTime3Start: [{ required: true, message: '请选择工作时间3开始时间', trigger: 'change' }],
        workTime3End: [{ required: true, message: '请选择工作时间3结束时间', trigger: 'change' }],
      },
    }
  },
  computed: {
    weekDaysOptions() {
      const options = {}
      this.weekDays.forEach((day) => {
        options[day.value] = day.label
      })
      return options
    },
  },
  watch: {
    data: {
      handler(data) {
        if (data) {
          this.form = {
            ...data,
            workDays: data.workDays?.split(',') || [],
          }
        } else {
          this.reset()
        }
      },
      immediate: true,
    },
  },
  created() {
    this.updateTimeRules()
  },
  methods: {
    updateTimeRules() {
      for (let i = 1; i <= 3; i++) {
        // 如果开始时间填写了，结束时间必须填写
        this.rules[`workTime${i}Start`] = [
          {
            validator: (rule, value, callback) => {
              if ((value && !this.form[`workTime${i}End`]) || (!value && this.form[`workTime${i}End`])) {
                callback(new Error(`请填写完整时间区间`))
              } else if (value && this.form[`workTime${i}End`]) {
                let baseDate = '2000-01-01'
                let startTime = new Date(`${baseDate} ${value}`)
                let endTime = new Date(`${baseDate} ${this.form[`workTime${i}End`]}`)

                if (startTime > endTime) {
                  callback(new Error(`工作时间${i}的结束时间必须晚于开始时间`))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },
            trigger: 'change',
          },
        ]
      }
    },
    // addTimeSlot() {
    //   if (this.form.timeSlots.length < 4) {
    //     this.form.timeSlots.push({
    //       startTime: '',
    //       endTime: '',
    //     })
    //   }
    // },
    // removeTimeSlot(index) {
    //   if (this.form.timeSlots.length > 1) {
    //     this.form.timeSlots.splice(index, 1)
    //   }
    // },
    reset() {
      this.form = {
        isEnabled: '1',
        workTime1Start: '08:30:00',
        workTime1End: '12:00:00',
        workTime2Start: '13:30:00',
        workTime2End: '18:00:00',
        workTime3Start: '',
        workTime3End: '',
        workDays: ['1', '2', '3', '4', '5'],
        skipHoliday: 1,
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      if (
        [1, 2, 3].some((t) => {
          let baseDate = '2000-01-01'
          let startTime = new Date(`${baseDate} ${this.form[`workTime${t}Start`]}`)
          let endTime = new Date(`${baseDate} ${this.form[`workTime${t}End`]}`)
          return [1, 2, 3].some((t2) => {
            let startTime2 = new Date(`${baseDate} ${this.form[`workTime${t2}Start`]}`)
            let endTime2 = new Date(`${baseDate} ${this.form[`workTime${t2}End`]}`)
            return t !== t2 && startTime >= startTime2 && startTime < endTime2
          })
        })
      ) {
        this.$message.error(`工作时间配置的时间区间重叠，请检查`)
        return
      }

      if ([1, 2, 3].every((t) => !this.form[`workTime${t}Start`] && !this.form[`workTime${t}End`])) {
        this.$confirm('未设置工作时间段，是否全天服务？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      }

      this.loading = true

      // 提交工作时间配置申请
      let submitData = {
        ...this.form,
        workDays: this.form.workDays.join(),
      }

      if (!this.fromWhere) {
        submitData = {
          ...submitData,
          workTimeId: this.data?.id,
          phoneNumberIds: this.selection.join(),
        }
      }

      let api = 
        this.fromWhere === 'myApplyDetail'
          ? myApplyUpdateWorkTime 
          : this.data
          ? updateWorkTime
          : addWorkTime

      const [err, res] = await api(submitData)

      if (res) {
        this.$message({
          message: '提交成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.time-slot-container {
  @include flex-row(flex-start, center);
  gap: 8px;

  .add-btn,
  .remove-btn {
    @include flex-center;
    width: 48px;
    height: 40px;
    padding: 12px 16px;
  }
}

.type-card {
  width: 314px;
  height: 118px;
  border-radius: 4px;
  cursor: pointer;

  @for $index from 1 through 2 {
    &:nth-child(#{$index}) {
      background: url('@/assets/images/number/worktime-config-card/config-#{$index}-disactive.svg') no-repeat center center / cover;

      &.active {
        background: url('@/assets/images/number/worktime-config-card/config-#{$index}-active.svg') no-repeat center center / cover;
      }
    }
  }
}
</style>
