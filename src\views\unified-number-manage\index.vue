<template>
  <base-card
    id="unified-number-manage"
    class="y-page">
    <aside-bar
      ref="asideBar"
      @set-current="setCurrent($event)"></aside-bar>
    <base-card class="right-panel y-container no-padding">
      <div class="y-header">
        <h2 class="y-title">统签号码管理</h2>
        <template v-if="isBatch">
          <el-button
            type="primary"
            plain
            size="small"
            @click="cancelBatch">
            取消
          </el-button>
          <el-button
            type="primary"
            plain
            size="small"
            :disabled="!currentGroupId"
            @click="handleExport">
            批量导出号码
          </el-button>
          <el-button
            :key="0"
            type="danger"
            plain
            size="small"
            :disabled="!selection.length"
            @click="handleBatchDelete">
            批量删除号码
          </el-button>
        </template>
        <template v-else>
          <el-button
            type="primary"
            plain
            size="small"
            @click="isBatch = true">
            <svg-icon icon="batch"></svg-icon>
            批量操作
          </el-button>
          <el-button
            type="primary"
            size="small"
            :disabled="!currentGroupId"
            @click="handleOperation(null)">
            <svg-icon icon="add"></svg-icon>
            新增统签号码
          </el-button>
        </template>
      </div>
      <div class="y-container--tight">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="searchForm"
          :rules="rules"
          size="small"
          style="margin-top: 16px">
          <el-form-item
            label="号码"
            prop="number">
            <el-input
              v-model="searchForm.number"
              clearable
              style="width: 220px"
              placeholder="请输入号码进行搜索"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              size="small"
              plain
              @click.native="resetSearch">
              <svg-icon icon="reset"></svg-icon>
              重置
            </el-button>
            <el-button
              v-debounce="fetchData"
              type="primary">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="table"
          :data="list"
          v-loading="loading"
          stripe
          height="100%"
          fit
          style="width: 100%"
          @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            width="55" />
          <el-table-column
            label="序号"
            type="index"
            width="70" />
          <el-table-column
            label="号码"
            prop="phoneNumber" />
          <el-table-column
            label="所属单位"
            prop="groupName" />
          <el-table-column
            label="创建时间"
            prop="createTime" />
          <el-table-column
            label="操作"
            width="100">
            <template #default="scope">
              <el-link
                @click="handleDelete(scope.row)"
                type="danger"
                :underline="false"
                >删除</el-link
              >
            </template>
          </el-table-column>
          <template #empty>
            <el-empty :description="currentGroupId ? '暂无信息' : '请先选择组织单位'"></el-empty>
          </template>
        </el-table>
      </div>
      <div class="y-footer">
        <pagination
          :current-page.sync="formData.pageIndex"
          :page-size.sync="formData.pageSize"
          :total="total"
          @page="fetchData"></pagination>
      </div>
    </base-card>
    <!-- 添加 Drawer 组件 -->
    <drawer
      :visible.sync="drawerVisible"
      :group-id="currentGroupId"
      @success="fetchData" />
  </base-card>
</template>

<script>
import Drawer from './components/Drawer.vue'
import AsideBar from '@/components/OrgTreePanel'
import { getUnifiedPhoneNumberList, deleteUnifiedPhoneNumber, exportUnifiedPhoneNumber } from '@/api/unified-number-manage'

export default {
  name: 'UnifiedNumberManage',
  components: {
    AsideBar,
    Drawer,
  },
  data() {
    return {
      loading: false,
      isBatch: false,
      searchForm: {
        number: '',
      },
      rules: {},
      formData: {
        pageIndex: 1,
        pageSize: 15,
        pageType: 3,
      },
      list: [],
      total: 0,
      drawerVisible: false,
      drawerData: null,
      selection: [],
      currentGroupId: null,
    }
  },
  created() {
    // 等待组织树选择后再加载数据
  },
  methods: {
    async fetchData() {
      // 如果没有选择组织，则不加载数据
      if (!this.currentGroupId) {
        this.list = []
        this.total = 0
        return
      }

      this.loading = true

      const payload = {
        ...this.searchForm,
        ...this.formData,
        groupId: this.currentGroupId,
      }

      const [err, res] = await getUnifiedPhoneNumberList(payload)
      if (res) {
        this.list = res.data || []
        this.total = res.totalRow || 0
      }
      this.loading = false
    },
    resetSearch() {
      this.$refs.searchForm?.resetFields()
      this.formData = {
        pageSize: 15,
        pageIndex: 1,
        pageType: 3,
      }
      this.fetchData()
    },
    handleOperation(row) {
      if (!this.currentGroupId) {
        this.$message.warning('请先选择组织单位')
        return
      }
      this.drawerVisible = true
      this.drawerData = row
    },
    async handleDelete(row) {
      await this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      this.loading = true
      const [err, res] = await deleteUnifiedPhoneNumber({
        phoneNumberIds: row.id,
      })
      this.loading = false

      if (!err) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    handleSelectionChange(selection) {
      this.selection = selection
    },
    async handleBatchDelete() {
      if (!this.selection.length) {
        this.$message.warning('请选择要删除的记录')
        return
      }

      try {
        await this.$confirm('此操作将永久删除选中的记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        this.loading = true
        const phoneNumberIds = this.selection.map((row) => row.id).join(',')
        const [err, res] = await deleteUnifiedPhoneNumber({
          phoneNumberIds,
        })
        this.loading = false

        if (!err) {
          this.$message({
            message: '删除成功！',
            type: 'success',
            duration: 800,
            onClose: () => {
              this.fetchData()
            },
          })
        }
      } catch (e) {}
    },
    async handleExport() {
      if (!this.currentGroupId) {
        this.$message.warning('请先选择组织单位')
        return
      }

      // 显示正在导出的loading消息
      const loadingMessage = this.$message({
        message: '正在导出...',
        duration: 0, // 不自动关闭
        type: 'info',
        iconClass: 'el-icon-loading',
      })

      const payload = {
        ...this.searchForm,
        groupId: this.currentGroupId,
      }
      const [err, res] = await exportUnifiedPhoneNumber(payload)

      // 关闭loading消息
      loadingMessage.close()
      if (res) {
        this.$message({
          message: '导出任务已创建，请稍后查看下载任务',
          type: 'success',
        })
      }
    },
    setCurrent(groupId) {
      this.currentGroupId = groupId
      this.fetchData()
    },
    cancelBatch() {
      this.isBatch = false
      this.selection = []
    },
  },
}
</script>

<style lang="scss" scoped>
#unified-number-manage {
  @include flex-row;
  background-color: transparent;

  > .aside-bar {
    @include card;
    margin-right: 16px;
  }

  @import '@/assets/styles/modules/table-page.scss';
}
</style>
