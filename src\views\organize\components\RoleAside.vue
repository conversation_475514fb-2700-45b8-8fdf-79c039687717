<template>
  <div class="w-[264px] h-full bg-white rounded-l">
    <div class="h-[56px] flex items-center justify-between px-6 border-transparent border-b-[1px] border-solid border-b-[#E0E0E0]">
      <div class="color-[#262626] font-bold text-[16px]">角色类型</div>
      <el-link type="primary" :underline="false" @click="handleAddRole">
        <i class="el-icon-plus"></i>
        创建自定义角色
      </el-link>
    </div>
    <div class="h-[calc(100%-56px)] overflow-auto px-6 py-4">
      <div :class="['item', current.ROLE_ID === item.ROLE_ID ? 'active' : '']" v-for="(item, index) in roles" :key="item.ROLE_ID" @click="setCurrent(item)">
        <div class="name">{{ item.ROLE_NAME }}</div>
        <div class="btns" v-show="item.SYS_FLAG == '2' && (current.ROLE_ID === item.ROLE_ID)">
          <i class="el-icon-edit text-white cursor-pointer" @click.stop="editRole(item)"></i>
          <div class="w-[1px] h-[12px] bg-white mx-[8px]"></div>
          <i class="el-icon-delete text-white cursor-pointer" @click.stop="delRole(item)"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRoleList, roleDel } from '@/api/org'

export default {
  name: 'RoleAside',
  data() {
    return {
      roles: [],
      current: {},
    }
  },
  methods: {
    async fetchData() {
      const [err, res] = await getRoleList({pageType: '3', pageNo: 1, pageSize: 100})
      if (res) {
        this.roles = res.data
        if (this.roles.length) {
          this.setCurrent(this.roles[0])
        }
      }
    },
    handleAddRole() {
      this.$emit('edit-role', {})
    },
    setCurrent(data) {
      this.current = Object.assign({}, data)
      this.$emit('set-current', data)
    },
    async delRole(data) {
      await this.$confirm('是否删除该角色?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      const [err, res] = await roleDel({ roleId: data.ROLE_ID })
      if (res) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
    },
    editRole(data) {
      this.$emit('edit-role', data)
    }
  },
  created() {
    this.fetchData()
  }
}
</script>

<style lang="scss" scoped>
.item {
  position: relative;
  height: 72px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  padding: 0 18px;
  border-radius: 4px;
  border: 2px solid transparent;
  cursor: pointer;
  .btns {
    position: absolute;
    right: 8px;
    top: 8px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 0 8px;
    height: 24px;
    background: rgba(29, 33, 41, 0.35);
  }
  &.active {
    .name {
      color: #0555CE;
    }
    border-color: #0555CE;
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 0;
      transform: translate(8px, -50%);
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-left: 8px solid #0555CE;
    }
  }
  &:last-child {
    margin-bottom: 0;
  }
  &:nth-child(n) {
    background: url('~@/assets/images/organize/role-1.png') no-repeat center center;
    background-size: 100% 100%;
  }
  &:nth-child(n+1) {
    background: url('~@/assets/images/organize/role-2.png') no-repeat center center;
    background-size: 100% 100%;
  }
  &:nth-child(n+3) {
    background: url('~@/assets/images/organize/role-3.png') no-repeat center center;
    background-size: 100% 100%;
  }
  &:nth-child(n+4) {
    background: url('~@/assets/images/organize/role-4.png') no-repeat center center;
    background-size: 100% 100%;
  }
  &:nth-child(n+5) {
    background: url('~@/assets/images/organize/role-5.png') no-repeat center center;
    background-size: 100% 100%;
  }
  .name {
    font-size: 16px;
    font-weight: bold;
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>