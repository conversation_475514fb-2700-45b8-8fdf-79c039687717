<template>
  <base-card
    class="record-card card-container y-container"
    border>
    <div
      v-if="data"
      class="info">
      <div class="btn-set">
        <record
          :src="getAudioFilePath(data.id)"
          class="mr-2 flex-1" />
        <el-button
          type="danger"
          plain
          size="small"
          @click="$emit('clear', data)"
          >清空</el-button
        >
        <el-button
          type="primary"
          plain
          size="small"
          @click="$emit('edit', data)"
          >修改</el-button
        >
      </div>
      <div class="y-bar no-padding">
        <span class="label">修改时间：{{ data.updateTime || data.createTime || '-' }}</span>
      </div>
    </div>
    <el-link
      v-else
      type="primary"
      :underline="false"
      @click="$emit('add')">
      <svg-icon icon="add"></svg-icon>
      添加
    </el-link>
  </base-card>
</template>

<script>
import Record from '@/components/Record'
import { getAudioFilePath } from '@/api/number-manage'

export default {
  props: {
    data: {
      type: Object,
      default: () => null,
    },
  },
  components: {
    Record,
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    getAudioFilePath,
  },
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/modules/info-card.scss';

.card-container {
  @include flex-center;
  padding: 16px 24px;
  min-height: 96px;
  background: $bgColor;

  .btn-set {
    justify-content: flex-start;
  }

  .label {
    color: $txtColor-light;
  }
}
</style>
