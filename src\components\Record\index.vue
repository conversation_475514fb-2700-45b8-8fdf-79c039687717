<template>
  <div class="record record-container">
    <div
      class="btn"
      @click="handlePlay">
      <el-image
        :src="isPlaying ? pauseIcon : playIcon"
        style="width: 24px; height: 24px" />
    </div>
    <div class="wave">
      <canvas
        v-if="loaded"
        ref="canvas"
        @click="handleClickWave"
        width="100%"
        height="16px"
        style="width: 100%; cursor: pointer"></canvas>
      <el-image
        v-else
        :src="require('@/assets/images/record/wave.svg')"
        fit="cover"
        style="height: 16px" />
    </div>
    <div class="time">{{ loaded ? remain : '--:--' }}</div>
    <audio
      v-show="false"
      ref="audio"
      controls
      @loadedmetadata="handleLoadedMetadata"
      @timeupdate="handleTimeUpdate"
      @ended="handleEnded"
      style="position: absolute; top: -50px; left: 0; width: 300px; height: 30px"></audio>
  </div>
</template>

<script setup>
import { defineProps, ref, watch, onMounted, nextTick, computed, onBeforeUnmount } from 'vue'
import playIcon from '@/assets/images/record/play.svg'
import pauseIcon from '@/assets/images/record/pause.svg'

const props = defineProps({
  src: {
    type: String,
    default: '',
  },
  lazy: {
    type: Boolean,
    default: false,
  },
})

const isPlaying = ref(false)
const loaded = ref(false)
const duration = ref(0)
const currentTime = ref(0)
const waveData = ref([])

const audio = ref(null)
const canvas = ref(null)

const ctx = new AudioContext()
const analyser = ctx.createAnalyser()

// Audio
const remain = computed(() => {
  const remainSeconds = Math.max(0, duration.value - currentTime.value)
  const minutes = Math.floor(remainSeconds / 60)
  const seconds = Math.floor(remainSeconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

const handleLoadedMetadata = () => {
  if (audio.value.duration === Infinity || isNaN(audio.value.duration)) {
    // TODO: 解决duration infinity问题
    duration.value = 0
  } else {
    duration.value = audio.value.duration.toFixed(2)
  }
}

const handleTimeUpdate = () => {
  currentTime.value = audio.value.currentTime.toFixed(2)
  drawWave(waveData.value)
}

const handleEnded = () => {
  isPlaying.value = false
  currentTime.value = 0
  drawWave(waveData.value)
}

const handlePlay = () => {
  isPlaying.value = !isPlaying.value

  if (!loaded.value) {
    loadRecord()
  }

  if (isPlaying.value) {
    play()
  } else {
    pause()
  }
}

const play = (time) => {
  audio.value.play()
}

const pause = () => {
  audio.value.pause()
}

// Wave
const loadRecord = async () => {
  if (!props.src) {
    return false
  }
  loaded.value = true
  audio.value.src = props.src
  // const source = ctx.createMediaElementSource(audio.value)
  // source.connect(analyser)
  // analyser.connect(ctx.destination)
  analyser.fftSize = 512

  const response = await fetch(props.src)
  const arrayBuffer = await response.arrayBuffer()

  if (ctx.state === 'suspended') {
    await ctx.resume()
  }

  const audioBuffer = await ctx.decodeAudioData(arrayBuffer)

  nextTick(() => {
    waveData.value = getWaveData(audioBuffer)
    drawWave(waveData.value)
  })
}

const getWaveData = (audioBuffer) => {
  const channelData = audioBuffer.getChannelData(0)
  // 增加采样点数量以获得更细腻的波形
  const numberOfBars = Math.floor(canvas.value.width / 5) // 每5像素一个波形条
  const samplesPerBar = Math.floor(channelData.length / numberOfBars)
  const data = []

  for (let i = 0; i < numberOfBars; i++) {
    const start = i * samplesPerBar
    const end = Math.min(start + samplesPerBar, channelData.length)
    let min = 1.0
    let max = -1.0

    for (let j = start; j < end; j++) {
      const value = channelData[j]
      min = Math.min(min, value)
      max = Math.max(max, value)
    }

    data.push({ min, max })
  }

  return data
}

// 绘制圆角矩形的辅助函数
const drawRoundedBar = (ctx, x, y, width, height, radius) => {
  ctx.beginPath()
  ctx.moveTo(x + radius, y)
  ctx.lineTo(x + width - radius, y)
  ctx.arc(x + width - radius, y + radius, radius, -Math.PI / 2, 0)
  ctx.lineTo(x + width, y + height - radius)
  ctx.arc(x + width - radius, y + height - radius, radius, 0, Math.PI / 2)
  ctx.lineTo(x + radius, y + height)
  ctx.arc(x + radius, y + height - radius, radius, Math.PI / 2, Math.PI)
  ctx.lineTo(x, y + radius)
  ctx.arc(x + radius, y + radius, radius, Math.PI, -Math.PI / 2)
  ctx.closePath()
  ctx.fill()
}

const drawWave = (data) => {
  if (!canvas.value) {
    return
  }

  canvas.value.width = canvas.value.clientWidth

  const ctx = canvas.value.getContext('2d')
  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height)

  const margin = {
    left: 0,
    right: 0,
  }
  const width = canvas.value.width - margin.left - margin.right
  const height = canvas.value.height
  const centerY = height / 2

  // 计算进度位置
  const progressX = Math.max(margin.left, Math.min(canvas.value.width - margin.right, margin.left + (currentTime.value / duration.value) * width))

  // 设置波形参数
  const barWidth = 1 // 每个波形条的宽度
  const barGap = barWidth * 2 // 波形条之间的间隔
  const barSpacing = barWidth + barGap // 总间距
  const maxBarHeight = height // 最大波形高度为容器高度
  const cornerRadius = barWidth / 2 // 圆角半径

  // 找出所有波形数据中的最大振幅，用于归一化
  const maxAmplitude = Math.max(...data.map((point) => Math.abs(point.max - point.min)))

  // 计算repeat值：数据长度与画布可容纳的波形条数量的比值
  const maxBarsInCanvas = Math.floor(width / barSpacing)
  const repeat = data.length / maxBarsInCanvas

  // 根据repeat值调整绘制策略
  if (repeat > 1) {
    // 数据点多于画布容量，需要采样绘制
    const step = repeat // 采样步长
    for (let i = 0; i < maxBarsInCanvas; i++) {
      const dataIndex = Math.floor(i * step)
      if (dataIndex >= data.length) break

      const point = data[dataIndex]
      const x = margin.left + i * barSpacing

      // 计算波形高度（归一化后乘以最大高度）
      const amplitude = Math.abs(point.max - point.min)
      const normalizedHeight = (amplitude / maxAmplitude) * maxBarHeight
      const barHeight = Math.max(1, normalizedHeight)

      // 确定是否在播放进度之前
      const isPlayed = x <= progressX

      // 设置渐变色
      if (isPlayed) {
        ctx.fillStyle = '#0555CE'
      } else {
        ctx.fillStyle = '#c5c5c5'
      }

      // 绘制圆角波形条
      const y = centerY - barHeight / 2
      drawRoundedBar(ctx, x, y, barWidth, barHeight, cornerRadius)
    }
  } else {
    // 数据点少于或等于画布容量，需要重复绘制以填满画布
    const repeatCount = Math.ceil(1 / repeat) // 需要重复的次数
    const scaledSpacing = width / (data.length * repeatCount) // 调整间距以填满画布

    for (let r = 0; r < repeatCount; r++) {
      data.forEach((point, i) => {
        const x = margin.left + (r * data.length + i) * scaledSpacing

        // 如果超出画布宽度则停止绘制
        if (x + barWidth > width + margin.left) return

        // 计算波形高度（归一化后乘以最大高度）
        const amplitude = Math.abs(point.max - point.min)
        const normalizedHeight = (amplitude / maxAmplitude) * maxBarHeight
        const barHeight = Math.max(1, normalizedHeight)

        // 确定是否在播放进度之前
        const isPlayed = x <= progressX

        // 设置渐变色
        if (isPlayed) {
          ctx.fillStyle = '#0555CE'
        } else {
          ctx.fillStyle = '#c5c5c5'
        }

        // 绘制圆角波形条
        const y = centerY - barHeight / 2
        drawRoundedBar(ctx, x, y, barWidth, barHeight, cornerRadius)
      })
    }
  }

  // // 绘制进度指示器
  // // 粉色竖线
  // const lineGradient = ctx.createLinearGradient(0, 0, 0, height)
  // lineGradient.addColorStop(0, '#ff89b7')
  // lineGradient.addColorStop(0.5, '#ee4586')
  // lineGradient.addColorStop(1, '#dd0559')
  // ctx.strokeStyle = lineGradient
  // ctx.lineWidth = 2

  // // 绘制从顶部到圆形指示器顶部的线
  // const markerY = height - 8
  // ctx.beginPath()
  // ctx.moveTo(progressX, 0)
  // ctx.lineTo(progressX, markerY - 8) // 停在圆形指示器上方
  // ctx.stroke()

  // // 保存当前上下文状态
  // ctx.save()

  // // 创建圆形裁剪区域以确保渐变只在圆内显示
  // ctx.beginPath()
  // ctx.arc(progressX, markerY, 8, 0, Math.PI * 2)
  // ctx.clip()

  // // 绘制粉色大圆背景
  // const bgColor1 = ctx.createLinearGradient(progressX - 8, markerY - 8, progressX + 8, markerY + 8)
  // bgColor1.addColorStop(0, '#d70156')
  // bgColor1.addColorStop(0.5, '#aa105e')
  // bgColor1.addColorStop(1, '#c60358')
  // ctx.fillStyle = bgColor1
  // ctx.fill()

  // // 绘制白色小圆
  // ctx.beginPath()
  // ctx.arc(progressX, markerY, 3, 0, Math.PI * 2)
  // ctx.fillStyle = '#ffffff'
  // ctx.fill()
}

const handleClickWave = (e) => {
  const rect = canvas.value.getBoundingClientRect()
  const x = e.clientX - rect.left
  const canvasWidth = rect.width
  const percent = Math.max(0, Math.min(1, x / canvasWidth))
  currentTime.value = percent * duration.value
  audio.value.currentTime = currentTime.value
}

watch(
  () => props.src,
  () => {
    if (!props.lazy) {
      loadRecord()
    }
  }
)

onMounted(() => {
  if (!props.lazy) {
    loadRecord()
  }
})

onBeforeUnmount(() => {
  if (ctx) {
    ctx.close()
  }
})
</script>

<style lang="scss" scoped>
.record-container {
  @include flex-row;
  position: relative;
  padding: 4px 8px;
  max-width: 290px;
  height: 32px;
  border-radius: 4px;
  background: transparentize($themeColor, 0.92);

  .btn {
    height: 24px;
    cursor: pointer;
  }

  .wave {
    @include flex;
    align-items: center;
    flex: 1;
    padding: 0 8px;
    height: 24px;
  }

  .time {
    color: $txtColor-light;
  }
}
</style>
