import { post } from '@/http/request'

// 统签号码管理API

/**
 * 统签号码新增
 * @param {Object} data - 新增统签号码参数
 * @param {string} data.phoneNumbers - 号码，多个用逗号间隔
 * @returns {Promise} 返回统签号码新增请求的Promise对象
 */
export function addUnifiedPhoneNumber(data) {
  return post('/yc-govphonemgmt/servlet/phoneNumber?action=unifiedPhoneNumberAdd', { data })
}

/**
 * 统签号码删除
 * @param {Object} data - 删除统签号码参数
 * @param {string} data.phoneNumberIds - 号码ID，多个用逗号间隔
 * @returns {Promise} 返回统签号码删除请求的Promise对象
 */
export function deleteUnifiedPhoneNumber(data) {
  return post('/yc-govphonemgmt/servlet/phoneNumber?action=unifiedPhoneNumberDelete', { data })
}

/**
 * 统签号码导出
 * @param {Object} data - 导出统签号码参数
 * @param {string} data.phoneNumbers - 号码，多个用逗号间隔
 * @returns {Promise} 返回统签号码导出请求的Promise对象
 */
export function exportUnifiedPhoneNumber(data) {
  return post('/yc-govphonemgmt/servlet/phoneNumber?action=unifiedPhoneNumberExport', { data })
}

/**
 * 统签号码列表接口
 * @param {Object} data - 查询参数
 * @param {string} data.groupId - 单位ID
 * @param {string} [data.number] - 号码
 * @param {number} [data.pageIndex] - 页码
 * @param {number} [data.pageSize] - 每页数量
 * @param {number} [data.pageType] - 分页类型
 * @param {string} [data.keyword] - 关键词
 * @param {string} [data.beginDate] - 开始日期
 * @param {string} [data.endDate] - 结束日期
 * @returns {Promise} 返回统签号码列表查询请求的Promise对象
 */
export function getUnifiedPhoneNumberList(data) {
  return post('/yc-govphonemgmt/webcall?action=phoneNumber.unifiedList', { data })
}
