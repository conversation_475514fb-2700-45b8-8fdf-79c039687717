<template>
  <div>
    <div class="y-bar no-padding">
      <h4 class="y-title flex-1">录音白名单列表</h4>
      <el-form
        ref="searchForm"
        :inline="true"
        :model="searchForm"
        :rules="rules"
        size="small">
        <el-form-item
          label="号码"
          prop="phoneNum">
          <el-input
            v-model="searchForm.phoneNum"
            clearable
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="small"
            plain
            @click="handleReset">
            <svg-icon icon="reset"></svg-icon>
            重置
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="handleSearch">
            <i class="el-icon-search"></i>
            搜索
          </el-button>
          <el-button
            type="danger"
            plain
            size="small"
            :disabled="multipleSelection.length === 0"
            :loading="deleteLoading"
            @click="handleBatchDelete">
            <svg-icon icon="remove"></svg-icon>
            批量删除
          </el-button>
          <el-button
            type="primary"
            plain
            size="small"
            @click="handleAdd">
            <svg-icon icon="add"></svg-icon>
            添加白名单
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      ref="table"
      :data="list"
      v-loading="loading"
      stripe
      height="100%"
      fit
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55" />
      <el-table-column
        label="白名单号码"
        prop="whitelistNumber" />
      <el-table-column
        label="添加人"
        prop="createBy" />
      <el-table-column
        label="添加时间"
        prop="createTime" />
      <el-table-column
        label="备注"
        prop="remark" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-link
            @click="handleDelete(scope.row)"
            type="danger"
            :underline="false"
            >删除</el-link
          >
        </template>
      </el-table-column>
      <template #empty>
        <el-empty description="暂无信息"></el-empty>
      </template>
    </el-table>
    <div
      class="y-footer"
      style="padding-right: 0">
      <pagination
        :current-page.sync="formData.pageNum"
        :page-size.sync="formData.pageSize"
        :total="total"
        @page="fetchData"></pagination>
    </div>

    <!-- 白名单编辑抽屉 -->
    <recording-whitelist-drawer
      :visible.sync="drawerVisible"
      :selection="[phoneId]"
      @success="fetchData" />
  </div>
</template>

<script>
import { getWhitelistPage, deleteWhitelist } from '@/api/number-manage'
import RecordingWhitelistDrawer from '../modals/RecordingWhitelistDrawer'

export default {
  name: 'WhitelistTable',
  components: {
    RecordingWhitelistDrawer,
  },
  props: {
    phoneId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      deleteLoading: false,
      searchForm: {
        phoneNum: '',
      },
      rules: {},
      formData: {
        pageNum: 1,
        pageSize: 15,
        pageType: 3,
      },
      total: 0,
      list: [],
      drawerVisible: false,
      multipleSelection: [],
    }
  },
  watch: {
    phoneId: {
      handler(newVal) {
        if (newVal) {
          this.fetchData()
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取白名单列表
    async fetchData() {
      if (!this.phoneId) return

      this.loading = true
      const [err, res] = await getWhitelistPage({
        phoneId: this.phoneId,
        ...this.searchForm,
        ...this.formData,
      })

      if (res) {
        this.list = res.data || []
        this.total = res.totalRow || 0
      }
      this.loading = false
    },

    // 搜索
    handleSearch() {
      this.formData.pageNum = 1
      this.fetchData()
    },

    // 重置
    handleReset() {
      this.searchForm.phoneNum = ''
      this.formData.pageNum = 1
      this.fetchData()
    },

    // 表格选择项变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确认删除该白名单号码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.deleteLoading = true
        const [err, res] = await deleteWhitelist({
          phoneId: this.phoneId,
          whitePhoneNumIds: row.id
        })
        this.deleteLoading = false

        if (res) {
          this.$message.success('删除成功')
          this.fetchData()
        }
      }).catch(() => {
        // 取消删除
      })
    },

    // 批量删除
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择要删除的白名单')
        return
      }

      this.$confirm(`确认删除选中的 ${this.multipleSelection.length} 个白名单号码吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.deleteLoading = true
        const whitePhoneNumIds = this.multipleSelection.map(item => item.id).join(',')

        const [err, res] = await deleteWhitelist({
          phoneId: this.phoneId,
          whitePhoneNumIds
        })
        this.deleteLoading = false

        if (res) {
          this.$message.success('批量删除成功')
          this.fetchData()
        }
      }).catch(() => {
        // 取消删除
      })
    },

    // 添加白名单
    handleAdd() {
      this.drawerVisible = true
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
