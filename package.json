{"name": "y-admin", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --no-module", "lint": "vue-cli-service lint", "new": "plop", "inspect:prod": "vue-cli-service inspect --mode production", "inspect:dev": "vue-cli-service inspect --mode development"}, "dependencies": {"@vue/preload-webpack-plugin": "^2.0.0", "core-js": "^3.8.3", "echarts": "^5.4.3", "element-ui": "^2.15.13", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "vue": "^2.7.16", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "~5.0.8", "autoprefixer": "^10.4.21", "axios": "^1.4.0", "babel-plugin-component": "^1.1.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "plop": "^3.1.2", "postcss": "^8.5.6", "sass": "^1.32.7", "sass-loader": "^12.0.0", "svg-sprite-loader": "^6.0.11", "tailwindcss": "^3.4.17", "vue-cli-plugin-axios": "0.0.4", "vue-cli-plugin-element": "~1.0.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}