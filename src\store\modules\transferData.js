/**
 * 临时数据存储模块
 * 用于跨页面传递数据，数据在使用后可以选择性清除
 */
export default {
  namespaced: true,
  state: () => ({
    // 使用对象存储临时数据，key 为数据标识，value 为数据内容
    tempDataMap: {},
  }),
  mutations: {
    SET_TEMP_DATA: (state, { key, value }) => {
      state.tempDataMap[key] = value
    },
    REMOVE_TEMP_DATA: (state, key) => {
      delete state.tempDataMap[key]
    },
    CLEAR_ALL_TEMP_DATA: (state) => {
      state.tempDataMap = {}
    }
  },
  actions: {
    /**
     * 设置临时数据
     * @param {object} context - Vuex context
     * @param {object} payload - 包含 key 和 value 的对象
     * @param {string} payload.key - 数据标识
     * @param {any} payload.value - 数据内容
     */
    setTempData({ commit }, { key, value }) {
      commit('SET_TEMP_DATA', { key, value })
    },
    
    /**
     * 获取临时数据
     * @param {object} context - Vuex context
     * @param {object} payload - 包含 key 和 autoRemove 的对象
     * @param {string} payload.key - 数据标识
     * @param {boolean} payload.autoRemove - 获取后是否自动移除，默认为 true
     * @returns {any} 临时数据内容
     */
    getTempData({ state, commit }, { key, autoRemove = true }) {
      const value = state.tempDataMap[key]
      if (autoRemove) {
        commit('REMOVE_TEMP_DATA', key)
      }
      return value
    },
    
    /**
     * 移除临时数据
     * @param {object} context - Vuex context
     * @param {string} key - 数据标识
     */
    removeTempData({ commit }, key) {
      commit('REMOVE_TEMP_DATA', key)
    },
    
    /**
     * 清空所有临时数据
     * @param {object} context - Vuex context
     */
    clearAllTempData({ commit }) {
      commit('CLEAR_ALL_TEMP_DATA')
    }
  },
  getters: {
    /**
     * 检查是否存在临时数据
     * @param {object} state - Vuex state
     * @returns {function} 返回一个函数，接收 key 参数
     */
    hasTempData: (state) => (key) => {
      return Object.prototype.hasOwnProperty.call(state.tempDataMap, key)
    },
  }
} 