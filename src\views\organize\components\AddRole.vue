<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="auto">
        <el-form-item
          label="角色名称"
          prop="roleName">
          <el-input
            v-model="form.roleName"
            placeholder="请输入"
            clearable />
        </el-form-item>

        <el-form-item
          label="角色描述"
          prop="roleDesc">
          <el-input
            v-model="form.roleDesc"
            type="textarea"
            maxlength="1000"
            :rows="10"
            resize="none"
            show-word-limit
            placeholder="请输入" />
        </el-form-item>
        <el-form-item
          label="权限配置"
          prop="resIds"
          class="resBox">
            <div class="border-[#E0E0E0] border-[1px] border-solid rounded overflow-auto h-full">
              <class-tree
                ref="tree"
                :tree="resTree"
                :default-checked-keys="checkedKeys"
                id-prop="resId"
                node-key="resId"
                highlight-current
                show-checkbox
                @check="handleCheck"
                class="checkbox-tree"
                >
                  <template #default="{ node, data }">
                    <svg-icon
                      v-show="getNodeIcon(node, 'icon')"
                      :class="['node-icon', getNodeIcon(node, 'icon') ? 'fill' : '']"
                      :icon="getNodeIcon(node, 'icon')"></svg-icon>
                    <span class="node-name">{{ data.resName }}</span>
                  </template>
              </class-tree>
            </div>
        </el-form-item>
      </el-form>
      <div class="y-footer">
        <el-link
          type="primary"
          :underline="false"
          @click="handleReset">
          <svg-icon icon="reset"></svg-icon>
          重置
        </el-link>
        <div style="flex: 1"></div>
        <el-button
          type="primary"
          plain
          @click="handleClose"
          >取消</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit">
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import ClassTree from '@/components/ClassTree'
import { getRoleResTree, roleAdd, roleEdit } from '@/api/org'

export default {
  name: 'AddRole',
  components: {
    ClassTree,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    title() {
      return this.data.ROLE_ID ? '编辑自定义角色' : '创建自定义角色'
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.getRoleResTree()
      }
    },
    data: {
      handler(newVal) {
        console.log(newVal)
        let { ROLE_NAME, ROLE_DESC } = newVal
        this.form.roleName = ROLE_NAME
        this.form.roleDesc = ROLE_DESC
      },
      deep: true
    }
  },
  data() {
    return {
      loading: false,
      form: {
        roleName: '',
        roleDesc: '',
        resIds: []
      },
      rules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' },
        ],
        resIds: [
          { required: true, message: '请选择', trigger: 'change' },
        ]
      },
      resTree: [],
      checkedKeys: []
    }
  },
  methods: {
    reset() {
      this.form = {
        roleName: '',
        roleDesc: '',
        resIds: []
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleReset() {
      this.reset()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true
      this.form.resIds = this.$refs.tree.$refs.elTree.getCheckedNodes(false, false).map(item => item.resId)
      let api = this.data.ROLE_ID ? roleEdit : roleAdd
      const [err, res] = await api({ ...this.form, roleId: this.data.ROLE_ID })

      if (res) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
    async getRoleResTree() {
      const [err, res] = await getRoleResTree({roleId: this.data.ROLE_ID || ''})
      if (res) {
        this.resTree = res.data
        if (this.data.ROLE_ID) {
          this.checkedKeys = this.getCheckedResIds(this.resTree)
          this.form.resIds = this.checkedKeys
        }
      }
    },
    getNodeIcon(node, type) {
      const map = {
        icon: ['minus-round', 'plus-round'],
      }
      if (node.childNodes.length > 0) {
        if (node.expanded) {
          return map[type][0]
        } else {
          return map[type][1]
        }
      } else {
        return ''
      }
    },
    handleCheck(data, checked) {
      console.log(data, checked, this.$refs.tree.$refs.elTree.getCheckedNodes(false, false))
      this.form.resIds = checked.checkedKeys
    },
    getCheckedResIds(arr) {
      let result = [];
      
      function traverse(nodes) {
        for (const node of nodes) {
          if (node.checked) {
            result.push(node.resId);
          }
          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        }
      }
      
      traverse(arr);
      return result;
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .resBox {
  flex: 1;
  overflow: hidden;
  .el-form-item__content {
    height: 100%;
  }
}
::v-deep .checkbox-tree {
  .el-tree-node__content {
    position: relative;
    .el-checkbox {
      position: absolute;
      right: 0;
    }
  }
}
</style>