{{#if template}}
<template>
  <div class="{{ kebabCase name }}"></div>
</template>
{{/if}}

{{#if script}}
<script>
export default {
  name: '{{ properCase name }}',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
}
</script>
{{/if}}

{{#if style}}
<style lang="scss" scoped>
.{{ kebabCase name }} { }
</style>
{{/if}}
