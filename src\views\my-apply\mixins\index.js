export default {
  data() {
    return {
      // tab列表及申请列表box右上角样式
      applyTabList: [
        {
          label: "审核中",
          name: "queryApplyIngList",
          value: "1",
          style:
            "backgroundColor: rgba(250, 153, 4, 0.1);color: rgba(250, 153, 4, 1);",
        },
        {
          label: "审核通过",
          name: "queryApplyApprovedList",
          value: "2",
          style:
            "backgroundColor: rgba(82, 196, 26, 0.1);color: rgba(82, 196, 26, 1);",
        },
        {
          label: "审核不通过",
          name: "queryApplyRejectedList",
          value: "3",
          style:
            "backgroundColor: rgba(240, 56, 56, 0.1);color: rgba(240, 56, 56, 1);",
        },
      ],
      auditTabList: [
        {
          label: "待审核",
          name: "auditWaitList",
          value: "1",
          style:
            "backgroundColor: rgba(5, 85, 206, 0.1);color: rgba(5, 85, 206, 1);",
        },
        {
          label: "审核通过",
          name: "auditPassList",
          value: "2",
          style:
            "backgroundColor: rgba(82, 196, 26, 0.1);color: rgba(82, 196, 26, 1);",
        },
        {
          label: "审核不通过",
          name: "auditNoPassList",
          value: "3",
          style:
            "backgroundColor: rgba(240, 56, 56, 0.1);color: rgba(240, 56, 56, 1);",
        },
      ],
      // 业务类型样式列表
      businessTypeOptions: [
        {
          label: "号码操作",
          value: "1",
          action: "ApplyDao.queryApplyDetail",
          style:
            "backgroundColor: rgba(5, 85, 206, 0.1);color: rgba(5, 85, 206, 1);",
        },
        {
          label: "工作时间",
          value: "2",
          action: "ApplyDao.queryApplyWorkDetail",
          style:
            "backgroundColor: rgba(92, 124, 250, 0.1);color: rgba(92, 124, 250, 1);",
        },
        {
          label: "欢迎语",
          value: "3",
          action: "ApplyDao.queryWelcomeDetail",
          style:
            "backgroundColor: rgba(51, 154, 240, 0.1);color:  rgba(51, 154, 240, 1);",
        },
        {
          label: "遇忙提示",
          value: "4",
          action: "Busy.getBusyPromptDetailWithAudio",
          style:
            "backgroundColor: rgba(34, 184, 207, 0.1);color: rgba(34, 184, 207, 1);",
        },
        {
          label: "非工作时间提示",
          value: "5",
          action: "NoWorkTime.getNonWorkTimePromptDetail",
          style:
            "backgroundColor: rgba(32, 201, 151, 0.1);color: rgba(32, 201, 151, 1);",
        },
        {
          label: "满意度评价开关",
          value: "6",
          action: "Satisfaction.getSwitchStatusDetail",
          style:
            "backgroundColor: rgba(148, 216, 45, 0.1);color: rgba(148, 216, 45, 1);",
        },
        {
          label: "录音白名单",
          value: "7",
          action: "WhitelistDetail.getRecordingWhitelistDetail",
          style:
            "backgroundColor: rgba(252, 196, 25, 0.1);color: rgba(252, 196, 25, 1);",
        },
        {
          label: "呼入黑名单",
          value: "8",
          action: "callInBlackDao.callInBlackInfo",
          style:
            "backgroundColor: rgba(255, 146, 43, 0.1);color: rgba(255, 146, 43, 1);",
        },
        {
          label: "协同号码",
          value: "9",
          action: "synergyNumberDao.synergyNumberInfo",
          style:
            "backgroundColor: rgba(255, 107, 107, 0.1);color: rgba(255, 107, 107, 1);",
        },
        {
          label: "通话并发数",
          value: "10",
          action: "callNumberDao.callNumberInfo",
          style:
            "backgroundColor: rgba(132, 94, 247, 0.1);color: rgba(132, 94, 247, 1);",
        },
      ],

      closeStatusOptions: [
        { label: "未关闭", value: "0" },
        { label: "已关闭", value: "1" },
      ],
    };
  },
  computed: {
    tabList() {
      return this.fromWhere === "auditList"
        ? this.auditTabList
        : this.applyTabList;
    },

    // 返回审核状态中文或样式
    handleTabActiveContent() {
      return (key, val, valKey) => {
        const findItem = this.tabList.find((item) => item[valKey] == val);
        return findItem?.[key] || "";
      };
    },

    // 返回业务类型的中文或样式
    handleBusinessTypeContent() {
      return (type, val) => {
        const findItem = this.businessTypeOptions.find(
          (item) => item.value == val
        );
        return findItem?.[type] || "";
      };
    },

    // 返回关闭状态中文
    handleCloseStatusContent() {
      return (val) => {
        const findItem = this.closeStatusOptions.find(
          (item) => item.value == val
        );
        return findItem?.label || "";
      };
    },
  },
};
