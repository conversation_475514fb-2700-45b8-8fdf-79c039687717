<template>
  <base-card
    class="work-time-card card-container y-container"
    border>
    <div class="info">
      <h4 class="y-title">
        {{ title || '工作时间配置' }}
      </h4>
      <div class="body">
        <p class="y-item text-overflow">
          <span class="label">工作时间：</span>
          <span class="value">{{ formatTimeSlots }}</span>
        </p>
        <p class="y-item">
          <span class="label">工作日：</span>
          <span class="value">{{ formatDays }}</span>
        </p>
        <p class="y-item">
          <span class="label">跳过节假日：</span>
          <span class="value">{{ data.skipHoliday == 1 ? '是' : '否' }}</span>
        </p>
        <p class="y-item">
          <span class="label">添加时间：</span>
          <span class="value">{{ data.createTime || '' }}</span>
        </p>
      </div>
      <div class="btn-set">
        <span class="mr-2">是否启用</span>
        <el-switch
          :value="data.blacklistStatus"
          @click.native="$emit('status-change', { ...data, blacklistStatus: Number(!data.blacklistStatus) })" />
        <div class="flex-1"></div>
        <el-button
          type="danger"
          plain
          size="small"
          @click="$emit('delete', data)"
          >删除</el-button
        >
        <el-button
          type="primary"
          plain
          size="small"
          @click="$emit('edit', data)"
          >编辑</el-button
        >
      </div>
    </div>
  </base-card>
</template>

<script>
import { formatWorkTimeSlots, formatWorkDays } from '@/utils/format'

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {},
  data() {
    return {}
  },
  computed: {
    formatTimeSlots() {
      return formatWorkTimeSlots(this.data)
    },
    formatDays() {
      return formatWorkDays(this.data.workDays) || ''
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/modules/info-card.scss';

.card-container {
  flex: 0 0 528px;
  background: linear-gradient(180deg, rgba(5, 85, 206, 0.1) 0%, rgba(5, 85, 206, 0) 25%),
    url('@/assets/images/number/worktime-icon.svg') no-repeat top right / 132px 128px;

  .info {
    .body {
      @include flex-col(flex-start, flex-start);
      gap: 4px;

      .y-item {
        .label {
          display: inline-block;
          min-width: 84px;
        }
      }
    }
  }
}
</style>
