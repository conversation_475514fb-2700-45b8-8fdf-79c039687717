<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="auto">
        <el-form-item
          label="单位名称"
          prop="deptName">
          <el-input
            v-model="form.deptName"
            placeholder="请输入"
            clearable />
        </el-form-item>

        <el-form-item
          label="地址"
          prop="address">
          <el-input
            v-model="form.address"
            placeholder="请输入"
            clearable />
        </el-form-item>

        <el-form-item
          label="负责人姓名"
          prop="responsibleName">
          <el-input
            v-model="form.responsibleName"
            placeholder="请输入"
            clearable />
        </el-form-item>

        <el-form-item
          label="负责人联系方式"
          prop="responsibleNumber">
          <el-input
            v-model="form.responsibleNumber"
            placeholder="请输入"
            clearable />
        </el-form-item>
      </el-form>
      <div class="y-footer">
        <el-link
          type="primary"
          :underline="false"
          @click="handleReset">
          <svg-icon icon="reset"></svg-icon>
          重置
        </el-link>
        <div style="flex: 1"></div>
        <el-button
          type="primary"
          plain
          @click="handleClose"
          >取消</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit">
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addDept } from '@/api/org'

export default {
  name: 'AddDept',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    deptId: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      form: {
        deptName: '',
        address: '',
        responsibleName: '',
        responsibleNumber: '',
      },
      rules: {
        deptName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
        ]
      },
    }
  },
  computed: {
    title() {
      return '新建下级部门'
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          if (this.deptId) {
            // this.getDeptDetail()
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    reset() {
      this.form = {
        deptName: '',
        address: '',
        responsibleName: '',
        responsibleNumber: ''
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleReset() {
      this.reset()
    },
    async getDeptDetail() {
      if (!this.deptId) return
      // api ...
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true
      const [err, res] = await addDept({ ...this.form, pDeptId: this.deptId })

      if (res) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
