<template>
  <base-card
    id="collaborative-number-block"
    class="y-page">
    <div class="y-container no-padding">
      <div class="y-header">
        <h2 class="y-title">协同号码拦截库配置</h2>
        <el-button
          type="primary"
          size="small"
          @click="handleAdd">
          <svg-icon icon="add"></svg-icon>
          新增拦截号码
        </el-button>
      </div>
      <div class="y-container--tight">
        <el-form
          ref="searchForm"
          :inline="true"
          :model="searchForm"
          size="small"
          style="margin-top: 16px">
          <el-form-item
            label="号码"
            prop="phoneNumber">
            <el-input
              v-model="searchForm.phoneNumber"
              clearable
              style="width: 260px"
              placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              plain
              size="small"
              @click="resetSearch">
              <svg-icon icon="reset"></svg-icon>
              重置
            </el-button>
            <el-button
              v-debounce="fetchData"
              type="primary"
              size="small">
              <i class="el-icon-search"></i>
              搜索
            </el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="table"
          :data="list"
          v-loading="loading"
          stripe
          height="100%"
          fit
          style="width: 100%">
          <!-- <el-table-column
            type="selection"
            width="55" /> -->
          <el-table-column
            type="index"
            label="序号"
            width="80" />
          <el-table-column
            label="拦截号码"
            prop="blockNumber"
            min-width="150" />
          <el-table-column
            label="添加人"
            prop="createBy"
            width="120" />
          <el-table-column
            label="添加时间"
            prop="createTime"
            width="180" />
          <el-table-column
            label="备注"
            prop="remark"
            min-width="120">
            <template #default="scope">
              <span>{{ scope.row.remark || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="80"
            fixed="right">
            <template #default="scope">
              <el-link
                @click="handleDelete(scope.row)"
                type="danger"
                :underline="false">
                删除
              </el-link>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无信息"></el-empty>
          </template>
        </el-table>
      </div>
      <div class="y-footer">
        <pagination
          :current-page.sync="formData.pageIndex"
          :page-size.sync="formData.pageSize"
          :total="total"
          @page="fetchData"></pagination>
      </div>
    </div>

    <!-- 添加抽屉 -->
    <add-drawer
      :visible.sync="addDrawerVisible"
      @success="fetchData" />
  </base-card>
</template>

<script>
import { getCollaborativeNumberBlockPage, deleteCollaborativeNumberBlock } from '@/api/collaborative-number-block'
import AddDrawer from './components/AddDrawer.vue'

export default {
  name: 'CollaborativeNumberBlock',
  components: {
    AddDrawer,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        phoneNumber: '',
      },
      formData: {
        pageIndex: 1,
        pageSize: 10,
        pageType: 3,
      },
      list: [],
      total: 0,
      addDrawerVisible: false,
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true

      const payload = {
        ...this.searchForm,
        ...this.formData,
      }

      const [err, res] = await getCollaborativeNumberBlockPage(payload)
      if (res) {
        this.list = res.data || []
        this.total = res.totalRow || 0
      }
      this.loading = false
    },
    resetSearch() {
      this.$refs.searchForm?.resetFields()
      this.formData = {
        pageIndex: 1,
        pageSize: 10,
        pageType: 3,
      }
      this.fetchData()
    },
    handleAdd() {
      this.addDrawerVisible = true
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这条记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })

        const [err, res] = await deleteCollaborativeNumberBlock({
          numberId: row.id,
        })

        if (res) {
          this.$message.success('删除成功')
          this.fetchData()
        }
      } catch (error) {
        // 用户取消删除
      }
    },
  },
}
</script>

<style lang="scss" scoped>
#collaborative-number-block {
  @import '@/assets/styles/modules/table-page.scss';
}
</style>
