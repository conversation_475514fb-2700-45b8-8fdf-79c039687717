<template>
  <div>
    <div class="list" v-loading="loading" element-loading-text="加载中...">
      <div class="box" v-for="(item, index) in list" :key="item.phoneNumber">
        <img class="box-icon" src="@/assets/images/my-apply/num-box-icon.png" />

        <div class="text-lg font-bold">{{ item.phoneNumber }}</div>
        <div class="type-content">
          <div class="mt-px">号码类型：</div>
          <div :class="['type', { 'is-Random': item.numberType == '2' }]">
            {{ numberTypeMap[item.numberType] }}
          </div>
        </div>
        <div>
          <div>所属单位：</div>
          <div>{{ item.groupName }}</div>
        </div>
        <div>
          <div>号码来源：</div>
          <div>{{ numberSourceMap[item.numberSource] }}</div>
        </div>

        <div v-if="['3', '4', '5'].includes(formData.businessType + '')">
          <div>{{ handleAudioLabel }}：</div>
          <Record
            v-if="item.orgBusiId"
            :src="handleAudioUrl(item)"
            class="flex-1"
          />
        </div>

        <div v-if="formData.businessType == '6'">
          <div>原满意度评价：</div>
          <div>{{ switchStatusDict[formData.orgSwitchStatus]?.label }}</div>
        </div>

        <template v-if="formData.businessType == '9'">
          <div>
            <div>原协同号码1：</div>
            <div>{{ formData.orgCollaborativeNumber1 }}</div>
          </div>
          <div>
            <div>原协同号码2：</div>
            <div>{{ formData.orgCollaborativeNumber2 }}</div>
          </div>
        </template>

        <div v-if="formData.businessType == '10'">
          <div>原通话并发数：</div>
          <div>{{ formData.orgOncurrentCallLimit }}</div>
        </div>
      </div>
    </div>

    <pagination
      class="pagination-class"
      v-if="isShowPagination"
      :current-page.sync="pageData.pageIndex"
      :page-size.sync="pageData.pageSize"
      :total="pageData.total"
      @page="handleGetMultipleNumbersList"
    >
    </pagination>
  </div>
</template>
<script>
import { getMultipleNumbersList } from "@/api/my-apply.js";
import Record from "@/components/Record/index.vue";
import { switchStatusDict } from "@/utils/constant.js";
import applyMixins from "../../mixins/index.js";

export default {
  mixins: [applyMixins],
  components: { Record },
  props: {
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      pageData: {
        pageIndex: 1,
        pageSize: 15,
        total: 0,
      },
      list: [],
      numberTypeMap: { 1: "统签", 2: "散号" },
      numberSourceMap: { 1: "AS平台", 2: "第三方呼叫中心" },
      isShowPagination: false,
      switchStatusDict,
    };
  },
  watch: {
    formData: {
      async handler(val) {
        const phoneLen = val.phoneNumber?.split(",")?.length || 0;

        if (val.businessType == "1" && phoneLen > 1) {
          this.isShowPagination = true;
          await this.handleGetMultipleNumbersList();
        } else {
          this.isShowPagination = false;
          this.list = [val];
        }
      },
      deep: true,
    },
  },
  computed: {
    // 返回音频文件名
    handleAudioLabel() {
      if (!this.formData.businessType) return "";

      return this.formData.businessType == "3"
        ? "原欢迎语"
        : this.formData.businessType == "4"
        ? "原遇忙提示"
        : "原非工作时间提示";
    },

    // 返回音频地址
    handleAudioUrl() {
      return (item) => {
        return `/yc-govphonemgmt/servlet/play?action=playVoice&audioId=${item.orgBusiId}`;
      };
    },
  },
  methods: {
    // 多个号码操作时获取多个号码信息
    async handleGetMultipleNumbersList() {
      try {
        this.loading = true;
        const res = await getMultipleNumbersList({
          applicationId: this.formData.id,
          ...this.pageData,
        });

        if (res?.data) {
          this.list = res.data.list;
          this.pageData.total = res.data.total;
        }
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.list {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  grid-gap: 16px;
  max-height: 390px;
  overflow-y: auto;
  border-radius: 4px;

  .box {
    position: relative;
    padding: 16px;
    border-radius: 4px;
    opacity: 0.8;
    background: rgba(5, 85, 206, 0.05);

    > :nth-child(n + 3) {
      margin-top: 4px;
    }

    .box-icon {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 96px;
      height: 92px;
    }

    > div {
      @include flex(flex-start);

      > :first-child {
        flex-shrink: 0;
        color: $txtColor-light;
      }

      .type {
        padding: 2px 8px;
        border-radius: 4px;
        background: rgba(82, 196, 26, 0.1);
        color: rgba(82, 196, 26, 1);
        font-size: 12px;
      }

      .is-Random {
        background: rgba(5, 85, 206, 0.1);
        color: $themeColor;
      }
    }

    .type-content {
      @include flex(flex-start, flex-start);
    }
  }
}

:deep(.pagination-class) {
  margin-top: 24px;

  .el-pagination {
    text-align: right;

    .el-pagination__sizes {
      margin-right: 0;
    }
  }
}
</style>
