<template>
  <el-dialog
    :title="title"
    width="700px"
    :visible.sync="visible"
    @close="handleClose">
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="120px">
        <!-- 导入音频 -->
        <el-form-item
          label="导入音频"
          prop="file">
          <el-upload
            ref="upload"
            action=""
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            accept="audio/*"
            drag
            style="width: 100%">
            <svg-icon icon="upload"></svg-icon>
            <div class="el-upload__text">将音频文件拖到此处，或<em>点击上传</em></div>
            <div class="y-prefixer">（wav格式）</div>
          </el-upload>
          <Record
            v-if="audioPreviewUrl"
            :src="audioPreviewUrl"
            style="max-width: 100%" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button
        type="primary"
        size="small"
        plain
        @click="handleClose"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        :loading="loading"
        :disabled="!form.file"
        @click="handleSubmit">
        提交申请
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addAudioFile, updateAudioFile } from '@/api/number-manage'
import Record from '@/components/Record'

export default {
  name: 'RecordingUploadDialog',
  components: {
    Record,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => null,
    },
    selection: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      loading: false,
      audioPreviewUrl: '',
      form: {
        file: null,
      },
      rules: {
        file: [{ required: true, message: '请选择音频文件', trigger: 'change' }],
      },
    }
  },
  computed: {
    title() {
      const map = {
        1: '欢迎语配置申请',
        2: '遇忙提示配置申请',
        3: '非工作时间提示配置申请',
      }
      return map[this.type]
    },
  },
  watch: {
    data: {
      handler() {
        this.reset()
      },
      immediate: true,
    },
  },
  methods: {
    reset() {
      this.form = {
        file: null,
      }
      this.audioPreviewUrl = ''
      this.$refs.form && this.$refs.form.resetFields()
      this.$refs.upload && this.$refs.upload.clearFiles()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    beforeUpload(file) {
      // 验证文件类型
      const isAudio = file.type.startsWith('audio/')
      if (!isAudio) {
        this.$message.error('只能上传音频文件!')
        return false
      }

      // 验证文件大小 (限制为10MB)
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('音频文件大小不能超过 10MB!')
        return false
      }

      return false // 阻止自动上传
    },
    handleFileChange(file, fileList) {
      this.form.file = file.raw
      this.audioPreviewUrl = URL.createObjectURL(file.raw)
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true

      let api
      let payload
      if (this.data) {
        api = updateAudioFile
        payload = {
          ...this.form,
          audioId: this.data.id,
        }
      } else {
        api = addAudioFile
        payload = {
          ...this.form,
          phoneIds: this.selection.join(),
          audioType: this.type,
        }
      }

      const [err, res] = await api(payload)

      // 模拟上传成功
      this.$message({
        message: '操作成功',
        type: 'success',
        duration: 1000,
        onClose: () => {
          this.handleClose()
          this.$emit('success')
        },
      })
      this.loading = false
    },
  },
  beforeDestroy() {
    // 清理预览URL
    if (this.audioPreviewUrl) {
      URL.revokeObjectURL(this.audioPreviewUrl)
    }
  },
}
</script>

<style lang="scss" scoped>
.el-upload {
  .svg-icon {
    font-size: 32px;
    color: $themeColor;
  }
}
</style>
