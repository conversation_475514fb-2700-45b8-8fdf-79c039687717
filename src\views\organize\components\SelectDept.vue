<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="420px"
    append-to-body>
    <div class="y-container no-padding">
      <div class="box">
        <el-input v-model="keyword" placeholder="请输入关键字" size="small" class="mb-2"></el-input>

        <el-tree
          ref="tree"
          :data="menuList"
          :filter-node-method="filterNode"
          :props="{label: 'deptName'}"
          :default-expand-all="true">
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <el-radio v-model="value" :label="data.deptId">{{ node.label }}</el-radio>
            </span>
        </el-tree>
      </div>
      <div class="y-footer">
        <div style="flex: 1"></div>
        <el-button
          type="primary"
          plain
          @click="handleClose"
          >取消</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit">
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { getDeptTree } from '@/api/org'
import { findTreeNode } from '@/utils/index'

export default {
  name: 'SelectDept',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    deptId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      keyword: '',
      value: '',
      menuList: []
    }
  },
  computed: {
    title() {
      return '选择单位'
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getDeptTree()
        }
      },
      immediate: true,
    },
    keyword(val) {
      this.$refs.tree.filter(val);
    },
    deptId(val) {
      if (val) {
        this.value = val
      }
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.deptName.indexOf(value) !== -1;
    },
    async getDeptTree() {
      this.loading = true
      const [err, res] = await getDeptTree()
      if (res) {
        this.menuList = res.data || []
      }

      this.loading = false
    },
    handleClose() {
      this.value = ''
      this.keyword = ''
      this.menuList = []
      this.$emit('update:visible', false)
    },
    handleSubmit() {
      if (!this.value) {
        this.$message.error('请选择单位')
        return
      }
      const data = findTreeNode(this.menuList, this.value, { idKey: 'deptId' })
      this.$emit('select', data)
      this.handleClose()
    },
  },
}
</script>

<style lang="scss" scoped>
.box {
  flex: 1;
  border: 1px solid #E0E0E0;
  border-radius: 4px;
  margin: 16px;
  padding: 16px;
}
</style>
