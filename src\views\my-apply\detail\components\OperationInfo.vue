<template>
  <div>
    <div class="desc-box">
      <div class="label">操作说明：</div>
      <div class="value">{{ formData.applicationDesc }}</div>
    </div>

    <!-- 除了号码操作 -->
    <el-descriptions
      v-if="formData.businessType != '1'"
      class="descriptions-class"
      :column="2"
      border
    >
      <el-descriptions-item
        v-for="item in propList[formData.businessType]"
        :key="item.prop"
        :label="item.label"
        :span="item.span"
      >
        <div v-if="/workTime/.test(item.prop)">
          {{ handleWorkTime(item.prop) }}
        </div>

        <div v-else-if="item.prop === 'workDays'">
          {{ formatWorkDays(formData.workDays) }}
        </div>

        <div v-else-if="item.prop === 'skipHoliday'">
          {{ skipHolidayKeyMap[formData.skipHoliday] }}
        </div>

        <div v-else-if="item.prop === 'isEnabled'">
          {{ isEnabledKeyMap[formData.isEnabled] }}
        </div>

        <div v-else-if="item.prop === 'switchStatus'">
          {{ switchStatusDict[formData.switchStatus]?.label }}
        </div>

        <div v-else-if="item.prop === 'operationType'">
          {{ operationTypeKeyMap[formData.operationType] }}
        </div>

        <Record
          v-else-if="
            ['3', '4', '5'].includes(formData.businessType + '') &&
            formData[item.prop]
          "
          :src="audioUrl"
        />

        <div v-else>
          {{ formData[item.prop] }}
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
import Record from "@/components/Record/index.vue";
import { switchStatusDict } from "@/utils/constant.js";
import { formatWorkDays } from "@/utils/format.js";
import applyMixins from "../../mixins/index.js";

export default {
  mixins: [applyMixins],
  components: { Record },
  props: {
    formData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      propList: {
        2: [
          { label: "工作时间1", prop: "workTime1" },
          { label: "工作时间2", prop: "workTime2" },
          { label: "工作时间3", prop: "workTime3" },
          { label: "工作日", prop: "workDays" },
          { label: "是否跳过节假日", prop: "skipHoliday" },
          { label: "是否启用", prop: "isEnabled" },
        ],
        3: [{ label: "新欢迎语", prop: "busiId" }],
        4: [{ label: "新遇忙提示", prop: "busiId" }],
        5: [{ label: "新非工作时间提示", prop: "busiId" }],
        6: [{ label: "是否开启满意度评价", prop: "switchStatus" }],
        7: [
          { label: "录音白名单号码", prop: "whitelistNumber", span: 2 },
          { label: "操作类型", prop: "operationType" },
          { label: "备注", prop: "remark" },
        ],
        8: [
          { label: "呼入黑名单号码", prop: "blacklistNumber", span: 2 },
          { label: "操作类型", prop: "operationType" },
          { label: "备注", prop: "remark" },
          { label: "生效时间", prop: "effectiveTime" },
          { label: "失效时间", prop: "expiryTime" },
        ],
        9: [
          { label: "协同号码1", prop: "collaborativeNumber1" },
          { label: "协同号码2", prop: "collaborativeNumber2" },
        ],
        10: [{ label: "新通话并发数", prop: "concurrentCallLimit" }],
      },
      isPlay: false,
      skipHolidayKeyMap: { 0: "否", 1: "是" },
      isEnabledKeyMap: { 0: "否", 1: "是" },
      operationTypeKeyMap: { 1: "添加", 2: "删除", 3: "导入" },
      switchStatusDict,
    };
  },
  computed: {
    // 返回工作时间拼接
    handleWorkTime() {
      return (prop) => {
        if (this.formData[`${prop}Start`] && this.formData[`${prop}End`]) {
          return `${this.formData[`${prop}Start`]}-${
            this.formData[`${prop}End`]
          }`;
        }

        return "";
      };
    },
  },
  watch: {
    formData: {
      async handler(val) {
        this.audioUrl = `/yc-govphonemgmt/servlet/play?action=playVoice&audioId=${val.busiId}`;
      },
      deep: true,
    },
  },
  methods: {
    formatWorkDays,
  },
};
</script>
<style lang="scss" scoped>
.desc-box {
  @include flex(flex-start, flex-start);
  padding: 16px;
  border-radius: 4px;
  background: linear-gradient(
    270deg,
    rgba(5, 85, 206, 0) 0%,
    rgba(5, 85, 206, 0.05) 100%
  );

  .label {
    flex-shrink: 0;
    color: $txtColor-light;
  }

  .value {
    overflow-wrap: break-word;
  }
}

:deep(.descriptions-class) {
  margin-top: 16px;

  .el-descriptions-item__cell {
    border-color: #e0e0e0;
    color: #262626;
  }

  .el-descriptions-item__label {
    background: rgba(5, 85, 206, 0.05);
    width: 160px;
  }
}
</style>
