// 号码类型字典
export const numberTypeDict = [
  { value: '1', label: '统签', type: 'success' },
  { value: '2', label: '散号', type: 'primary' },
]

// 号码来源字典
export const numberSourceDict = [
  { value: '1', label: 'AS平台' },
  { value: '2', label: '第三方呼叫中心' },
]

// 开关状态字典
export const switchStatusDict = [
  { value: '1', label: '开启' },
  { value: '0', label: '关闭' },
]

// 星期字典
export const weekDays = [
  { value: '1', label: '周一' },
  { value: '2', label: '周二' },
  { value: '3', label: '周三' },
  { value: '4', label: '周四' },
  { value: '5', label: '周五' },
  { value: '6', label: '周六' },
  { value: '7', label: '周日' },
]

export const toMap = (dict) => {
  return dict.reduce((acc, cur) => {
    acc[cur.value] = cur;
    return acc;
  }, {});
};

export const numberTypeMap = toMap(numberTypeDict);
export const numberSourceMap = toMap(numberSourceDict);
export const switchStatusMap = toMap(switchStatusDict);
export const weekDaysMap = toMap(weekDays);