<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="100px">
        <el-form-item
          label="号码"
          prop="phoneNumbers">
          <el-input
            v-model="form.phoneNumbers"
            type="textarea"
            placeholder="请输入"
            :rows="20"
            clearable
            style="margin-bottom: -64px" />
          <div
            class="y-bar"
            style="position: relative">
            <span class="y-prefixer">多个号码请以顿号间隔</span>

            <el-upload
              action=""
              :auto-upload="false"
              accept=".xlsx,.xls"
              :on-change="handleImportNumbers"
              :multiple="false"
              :show-file-list="false"
              :limit="1">
              <el-button
                type="primary"
                plain
                size="small">
                <SvgIcon icon="go-to" />
                导入号码
              </el-button>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item
          label="所属单位"
          prop="groupId">
          <el-cascader
            v-model="form.groupId"
            :options="departmentOptions"
            :props="cascaderProps"
            :show-all-levels="false"
            placeholder="请选择所属单位"
            style="width: 100%"
            clearable
            @change="handleDepartmentChange">
          </el-cascader>
        </el-form-item>

        <el-form-item
          label="号码来源"
          prop="numberSource">
          <MultiSwitch
            v-model="form.numberSource"
            :options="sourceOptions" />
        </el-form-item>

        <el-form-item
          label="号码类型"
          prop="numberType">
          <el-select
            v-model="form.numberType"
            placeholder="请选择号码类型"
            style="width: 100%"
            clearable
            disabled>
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="y-footer">
        <el-button
          type="primary"
          size="small"
          plain
          @click="handleClose"
          >取消</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="loading"
          @click="handleSubmit">
          提交申请
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addUnifiedPhoneNumber } from '@/api/unified-number-manage'
import { importPhoneNumber } from '@/api/number-manage'
import { getDeptTree } from '@/api/org'

export default {
  name: 'AddNumberDrawer',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '新增统签号码',
    },
    groupId: {
      type: String,
      default: '',
    },
  },
  data() {
    const validateNumbers = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入号码'))
      } else {
        // 简单验证每行是否是有效的电话号码格式
        const lines = value.split(',').filter((line) => line.trim())
        const phoneReg = /^[\d-\s()]+$/
        const invalidLines = lines.filter((line) => !phoneReg.test(line.trim()))
        if (invalidLines.length > 0) {
          callback(new Error('请输入正确的号码格式'))
        }
        callback()
      }
    }

    return {
      loading: false,
      importDialogVisible: false,
      departmentOptions: [],
      typeOptions: [
        { value: 1, label: '统签' },
        { value: 2, label: '散号' },
      ],
      sourceOptions: {
        1: 'AS平台',
        2: '第三方呼叫中心',
      },
      form: {
        phoneNumbers: '',
        groupId: '',
        groupName: '',
        numberSource: 1,
        numberType: 2,
      },
      rules: {
        phoneNumbers: [{ required: true, validator: validateNumbers, trigger: 'blur' }],
        groupId: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
        numberSource: [{ required: true, message: '请选择号码来源', trigger: 'change' }],
        numberType: [{ required: true, message: '请选择号码类型', trigger: 'change' }],
      },
      cascaderProps: {
        value: 'deptId',
        label: 'deptName',
        children: 'children',
        emitPath: false,
        checkStrictly: true,
      },
    }
  },
  computed: {},
  watch: {
    visible(newVal) {
      if (newVal) {
        // 每次打开drawer时初始化groupId
        if (this.groupId) {
          this.form.groupId = this.groupId
          this.updateGroupName(this.groupId)
        }
      }
    },
  },
  created() {
    this.loadDepartmentOptions()
  },
  methods: {
    async loadDepartmentOptions() {
      const [err, res] = await getDeptTree()
      if (res && res.data) {
        // 直接使用树形结构数据
        this.departmentOptions = res.data || []

        // 如果有初始groupId，设置对应的groupName
        if (this.groupId) {
          this.updateGroupName(this.groupId)
        }
      }
    },

    updateGroupName(groupId) {
      // 在树形结构中查找对应的部门名称
      const findDeptName = (nodes, targetId) => {
        for (const node of nodes) {
          if (node.deptId === targetId) {
            return node.deptName
          }
          if (node.children && node.children.length > 0) {
            const found = findDeptName(node.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      const deptName = findDeptName(this.departmentOptions, groupId)
      if (deptName) {
        this.form.groupName = deptName
      }
    },

    handleDepartmentChange(value) {
      // 设置了emit-path="false"，直接获取选中的部门ID
      if (value) {
        this.form.groupId = value
        this.updateGroupName(value)
      } else {
        this.form.groupId = ''
        this.form.groupName = ''
      }
    },

    reset() {
      this.form = {
        phoneNumbers: '',
        groupId: '',
        groupName: '',
        numberSource: 1,
        numberType: 2,
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleImportNumbers(file) {
      // 添加loading状态
      const loading = this.$loading({
        lock: true,
        text: '正在导入号码...',
        spinner: 'el-icon-loading',
      })

      // 调用导入接口
      const [err, res] = await importPhoneNumber(file.raw)

      if (res && res.data) {
        // 导入成功，将号码填充到表单
        const importedNumbers = res.data
        if (importedNumbers.length > 0) {
          // 合并现有号码和新导入的号码
          let existingNumbers = []
          if (this.form.phoneNumbers) {
            existingNumbers = this.form.phoneNumbers.split(',').filter((num) => num.trim())
          }

          // 去重并合并
          const allNumbers = [...new Set([...existingNumbers, ...importedNumbers])]
          this.form.phoneNumbers = allNumbers.join(',')

          this.$message.success(`成功导入 ${importedNumbers.length} 个号码`)
        }
      }
      loading.close()
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      // 处理号码数据：将换行分隔的号码转换为逗号分隔
      const phoneNumbers = this.form.phoneNumbers.split(',').filter((line) => line.trim())
      const submitData = {
        ...this.form,
        phoneNumbers: phoneNumbers.join(),
      }

      this.loading = true
      const [err, res] = await addUnifiedPhoneNumber(submitData)

      if (res) {
        this.$message({
          message: '提交成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
