<template>
  <div class="aside-bar">
    <div class="y-header">
      <h2 class="y-title">组织机构</h2>
    </div>
    <class-tree
      ref="tree"
      :tree="menuList"
      id-prop="deptId"
      :active-item="activeItem"
      highlight-current
      :filter-node-method="filterNode"
      @set-current="setCurrent($event)"
      :loading="loading">
      <template #default="{ node, data }">
        <svg-icon
          v-show="getNodeIcon(node, 'icon')"
          :class="['node-icon', getNodeIcon(node, 'icon') ? 'fill' : '']"
          :icon="getNodeIcon(node, 'icon')"></svg-icon>
        <span class="node-name">{{ data.deptName }}</span>
        <i class="el-icon-plus" v-if="node.level === 1" @click.stop="handleAddDept(data)"></i>
        <el-popover
          placement="bottom-end"
          title=""
          popper-class="more-popover"
          width="140"
          trigger="click"
          content="">
          <div class="h-[40px] leading-[40px] flex items-center px-4 cursor-pointer" @click.stop="handleAddDept(data)">
            <i class="el-icon-plus mr-2"></i>
            <span>新建下级部门</span>
          </div>
          <div class="h-[40px] leading-[40px] flex items-center px-4 cursor-pointer" @click.stop="handleDelDept(data)">
            <i class="el-icon-delete mr-2"></i>
            <span>删除</span>
          </div>
          <i class="el-icon-more" slot="reference" v-if="node.level !== 1" @click.stop></i>
        </el-popover>
      </template>
    </class-tree>
  </div>
</template>

<script>
import { getDeptTree, delDept } from '@/api/org'
import ClassTree from '@/components/ClassTree'

export default {
  components: {
    ClassTree,
  },
  data() {
    return {
      loading: false,
      submitLoading: false,
      activeItem: null,
      keyword: '',
      menuList: [],
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true

      const [err, res] = await getDeptTree()
      if (res) {
        this.menuList = res.data || []
        if (this.menuList.length && this.menuList[0]?.children && this.menuList[0]?.children.length) {
          this.setCurrent(this.menuList[0].children[0])
        }
      }
      this.loading = false
    },
    setCurrent(item) {
      console.log(item, 'item')
      if (!item) return
      this.activeItem = item.deptId
      this.$emit('set-current', item)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.deptName.indexOf(value) !== -1
    },
    getNodeIcon(node, type) {
      const map = {
        icon: ['minus-round', 'plus-round'],
      }
      if (node.childNodes.length > 0) {
        if (node.expanded) {
          return map[type][0]
        } else {
          return map[type][1]
        }
      } else {
        return ''
      }
    },
    handleAddDept(data) {
      this.$emit('add-dept', data)
    },
    async handleDelDept(data) {
      await this.$confirm('是否删除该部门?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      this.loading = true
      const [err, res] = await delDept({ deptId: data.deptId })
      if (res) {
        this.$message({
          message: '删除成功！',
          type: 'success',
          duration: 800,
          onClose: () => {
            this.fetchData()
          },
        })
      }
      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.aside-bar {
  @include flex-col;
  flex-shrink: 0;
  width: 264px;
  height: 100%;

  :deep(.y-header) {
    .svg-icon {
      font-size: 16px;
      color: $txtColor-light;
      cursor: pointer;

      &:hover {
        color: $themeColor;
      }
    }
  }
}
</style>
<style lang="scss">
.more-popover {
  padding: 8px 0;
}
</style>
