@forward './variables.module.scss';
@use './variables.module.scss' as *;

@mixin full {
  width: 100%;
  height: 100%;
}

@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex($justify-content: space-between, $align-items: center) {
  display: flex;
  justify-content: $justify-content;
  align-items: $align-items;
}

@mixin flex-row($justify-content: space-between, $align-items: center) {
  @include flex($justify-content, $align-items);
  flex-direction: row;
}

@mixin flex-col($justify-content: space-between, $align-items: center) {
  @include flex($justify-content, $align-items);
  flex-direction: column;
}

@mixin container($v-padding: 24px, $h-padding: 24px, $justify-content: normal, $align-items: normal) {
  @include flex-col($justify-content, $align-items);
  flex: 1;
  padding: $v-padding $h-padding;
  width: 100%;
  height: 100%;
}

@mixin title {
  // width: max-content;
  font-size: 16px;
  font-weight: 600;
  // white-space: nowrap;
}

@mixin bar($v-padding: 12px, $h-padding: 24px, $justify-content: space-between, $align-items: center) {
  @include flex-row($justify-content, $align-items);
  flex-wrap: wrap;
  padding: $v-padding $h-padding;
  width: 100%;
}

@mixin text-overflow($line: 1) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  word-break: break-all;

  @if $line ==1 {
    white-space: normal;
  }
}

@mixin card {
  position: relative;
  background-color: $bgColor;
  border-radius: 4px;
}

@mixin card-wrapper($padding: 16px 0, $column-min: 408px, $row: max-content) {
  padding: $padding;
  grid-auto-rows: $row;
  grid-template-columns: repeat(auto-fill, minmax($column-min, 1fr));
}