<template>
  <el-drawer
    :title="title"
    :visible.sync="visible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="form"
        :rules="rules"
        size="large"
        label-width="120px">
        <el-form-item
          label="录音白名单号码"
          prop="whitePhoneNums">
          <el-input
            v-model="form.whitePhoneNums"
            type="textarea"
            placeholder="请输入"
            :rows="20"
            clearable
            style="margin-bottom: -64px" />
          <div
            class="y-bar"
            style="position: relative">
            <span class="y-prefixer">多个号码请以顿号间隔</span>

            <el-upload
              action=""
              :auto-upload="false"
              accept=".xlsx,.xls"
              :on-change="handleImportNumbers"
              :multiple="false"
              :show-file-list="false"
              :limit="1">
              <el-button
                type="primary"
                plain
                size="small">
                <SvgIcon icon="go-to" />
                导入白名单号码
              </el-button>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item
          label="备注"
          prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入"
            :rows="20"
            clearable />
        </el-form-item>
      </el-form>

      <div class="y-footer">
        <el-button
          type="primary"
          size="small"
          plain
          @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          size="small"
          :loading="loading"
          @click="handleSubmit">
          提交申请
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addWhitelist, importPhoneNumber } from '@/api/number-manage'

export default {
  name: 'RecordingWhitelistDrawer',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '录音白名单配置申请',
    },
    selection: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    const validateNumbers = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入号码'))
      } else {
        // 简单验证每行是否是有效的电话号码格式
        const lines = value.split(',').filter((line) => line.trim())
        const phoneReg = /^[\d-\s()]+$/
        const invalidLines = lines.filter((line) => !phoneReg.test(line.trim()))
        if (invalidLines.length > 0) {
          callback(new Error('请输入正确的号码格式'))
        }
        callback()
      }
    }

    return {
      loading: false,
      form: {
        whitePhoneNums: '',
        remark: '',
      },
      rules: {
        whitePhoneNums: [{ required: true, validator: validateNumbers, trigger: 'blur' }],
      },
    }
  },
  computed: {},
  methods: {
    reset() {
      this.form = {
        whitePhoneNums: '',
        remark: '',
      }
      this.$refs.form && this.$refs.form.resetFields()
    },
    handleClose() {
      this.reset()
      this.$emit('update:visible', false)
    },
    async handleImportNumbers(file) {
      // 添加loading状态
      const loading = this.$loading({
        lock: true,
        text: '正在导入号码...',
        spinner: 'el-icon-loading',
      })

      // 调用导入接口
      const [err, res] = await importPhoneNumber(file.raw)

      if (res && res.data) {
        // 导入成功，将号码填充到表单
        const importedNumbers = res.data
        if (importedNumbers.length > 0) {
          // 合并现有号码和新导入的号码
          let existingNumbers = []
          if (this.form.whitePhoneNums) {
            existingNumbers = this.form.whitePhoneNums.split(',').filter((num) => num.trim())
          }

          // 去重并合并
          const allNumbers = [...new Set([...existingNumbers, ...importedNumbers])]
          this.form.whitePhoneNums = allNumbers.join(',')

          this.$message.success(`成功导入 ${importedNumbers.length} 个号码`)
        }
      }
      loading.close()
    },
    async handleSubmit() {
      await this.$refs.form.validate()

      this.loading = true

      const phoneNumbers = this.form.whitePhoneNums.split(',').filter((line) => line.trim())
      const submitData = {
        ...this.form,
        whitePhoneNums: phoneNumbers.join(),
        phoneId: this.selection.join(),
      }

      const [err, res] = await addWhitelist(submitData)

      if (res) {
        this.$message({
          message: '提交成功',
          type: 'success',
          duration: 1000,
          onClose: () => {
            this.handleClose()
            this.$emit('success')
          },
        })
      }

      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
