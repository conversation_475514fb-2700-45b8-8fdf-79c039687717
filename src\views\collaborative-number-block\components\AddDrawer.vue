<template>
  <el-drawer
    title="新增协同号码拦截配置"
    :visible.sync="drawerVisible"
    :before-close="handleClose"
    size="700px"
    append-to-body>
    <div class="y-container no-padding">
      <el-form
        class="y-container--tight"
        ref="form"
        :model="formData"
        :rules="rules"
        size="large"
        label-width="80px">
        <el-form-item
          label="号码"
          prop="phoneNum">
          <el-input
            v-model="formData.phoneNum"
            placeholder="请输入号码"
            maxlength="20"
            show-word-limit
            clearable></el-input>
        </el-form-item>
        <el-form-item
          label="备注"
          prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="24"
            placeholder="请输入备注"
            maxlength="1000"
            show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div class="y-footer">
        <el-link
          type="primary"
          :underline="false"
          @click="handleReset">
          <svg-icon icon="reset"></svg-icon>
          重置
        </el-link>
        <div style="flex: 1"></div>
        <el-button
          type="primary"
          plain
          @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit">
          确定
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { addCollaborativeNumberBlock } from '@/api/collaborative-number-block'

export default {
  name: 'AddDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      submitLoading: false,
      formData: {
        phoneNum: '',
        remark: '',
      },
      rules: {
        phoneNum: [
          { required: true, message: '请输入号码', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur',
          },
        ],
        remark: [
          // { required: true, message: '请输入备注', trigger: 'blur' }
        ],
      },
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  methods: {
    async handleSubmit() {
      try {
        await this.$refs.form.validate()

        this.submitLoading = true
        const [err, res] = await addCollaborativeNumberBlock(this.formData)

        if (res) {
          this.$message.success('添加成功')
          this.$emit('success')
          this.handleClose()
        }
      } catch (error) {
        // 表单验证失败
      } finally {
        this.submitLoading = false
      }
    },
    handleClose() {
      this.drawerVisible = false
      this.resetForm()
    },
    handleReset() {
      this.resetForm()
    },
    resetForm() {
      this.$refs.form?.resetFields()
      this.formData = {
        phoneNum: '',
        remark: '',
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
